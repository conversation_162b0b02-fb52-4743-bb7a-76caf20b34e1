export const PRODUCT_ATTACHMENT_EVENT_BUS_INFO = {
  detailType: 'Update product attachment',
  source: 'dentalkart.catalog.updateProductAttachment',
};

export const PRODUCT_TAG_EVENT_BUS_INFO = {
  detailType: 'Update product tags',
  source: 'dentalkart.catalog.updateProductTags',
};

export const EVENT_BUS_ACTIONS = {
  UPDATE: 'update',
  CREATE: 'create',
  DELETE: 'delete',
  BULK_STATUS_UPDATE: 'bulk_update_status',
};

export const EVENT_BUS_SOURCE = {
  category: { staging: 'dev.dentalkart.catalog.category.service', prod: 'prod.dentalkart.catalog.category.service' },
  product: { staging: 'dev.dentalkart.catalog.service', prod: 'prod.dentalkart.catalog.service' },
  url_rewrite: {
    staging: 'dev.dentalkart.catalog.urlresolver.service',
    prod: 'prod.dentalkart.catalog.urlresolver.service',
  },
};
