import * as dotenv from 'dotenv';
dotenv.config();
export default {
  port: +process.env.PORT,
  awsKey: process.env.ACCESS_KEY_ID,
  awsSecret: process.env.SECRET_ACCESS_KEY,
  opensearchUrl: process.env.OPENSEARCH_DOMAIN_URL,
  apiKey: process.env.API_KEY,
  categoryIndexName: process.env.CATEGORY_INDEX_NAME,
  productIndexName: process.env.PRODUCT_INDEX_NAME,
  productAttributesIndexName: process.env.PRODUCT_ATTRIBUTES_INDEX_NAME,
  urlRewritesIndexName: process.env.URL_REWRITES_INDEX_NAME,
  resultSize: +process.env.QUERY_RESULT_SIZE,
  sqlQueryResultsize: +process.env.SQL_QUERY_RESULT_SIZE,
  magentoBaseImagesUrl: 'https://dentalkart-catalog-products.s3.ap-south-1.amazonaws.com/media/catalog/product',
  googleInternalYoutubeDataApiUrl:
    'https://www.googleapis.com/youtube/v3/videos',
  googleApiKey: process.env.GOOGLE_API_KEY,
  getCustomerInfo: process.env.GET_CUSTOMER_INFO_API,
  database: {
    host: process.env.MYSQL_HOST,
    port: +process.env.MYSQL_PORT,
    username: process.env.MYSQL_USERNAME,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE,
  },
  aws: {
    region: process.env.AWS_REGION,
    key: process.env.AWS_ACCESS_KEY,
    secret: process.env.AWS_SECRET_KEY,
    bucket: process.env.AWS_BUCKET_NAME,
    bucketBaseUrl: process.env.AWS_BUCKET_BASE_URL,
    productImagesFolderName: 'media/catalog/product',
    categoryImagesFolderName: 'media/catalog/category',
  },
  catalog_eventbus: {
    eventbusUrl: process.env.EVENTBUS_URL,
    eventbusAuthKey: process.env.EVENTBUS_KEY,
  },
  notifications_eventbus: {
    eventbusUrl: process.env.NOTIFICATIONS_EVENTBUS_URL,
    // eventbusAuthKey: process.env.EVENTBUS_KEY,
  },

  viniculum: {
    base_url: process.env.VINICULUM_BASE_URL,
    create_product_url:
      process.env.VINICULUM_BASE_URL + '/RestWS/api/eretail/v1/sku/create',
    update_product_url:
      process.env.VINICULUM_BASE_URL + 'RestWS/api/eretail/v1/sku/update',
    api_key: process.env.VINICULUM_API_KEY,
    owner: process.env.VINICULUM_OWNER,
  },
  outbox_api_constants: {
    products_count: 20,
    max_process_count: 5,
  },
  dk: {
    customer_base_url: process.env.CUSTOMER_BASE_URL,
    dental_brands_id: process.env.DENTAL_BRANDS_ID,
  },

  xray_daemon_address: process.env.XRAY_DAEMON_ADDRESS,
  service_name: process.env.SERVICE_NAME || 'Catalog_Service',
};
