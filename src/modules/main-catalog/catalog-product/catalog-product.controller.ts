import {
  Controller,
  Post,
  Body,
  Get,
  Patch,
  Delete,
  Param,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  Res,
  BadRequestException,
  InternalServerErrorException,
  ParseIntPipe,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Headers,
  ValidationPipe,
  Put,
} from '@nestjs/common';
import { Response } from 'express';
import * as path from 'path';
import { diskStorage } from 'multer';
import { CatalogProductService } from './catalog-product.service';
import * as fs from 'fs';
import {
  CreateCatalogProductAttributesDto,
  CreateCatalogProductDto,
  UpdateCatalogProductDto,
  UpdateCatalogProductAttributesDto,
  CreateAttributesGroupDto,
  UpdateAttributesGroupDto,
  CreateAttributesGroupRelationDto,
  AttributeDto,
  UpdateMultipleProductsStatusDto,
  ViniculumInventoryItemDto,
  GroupTierPriceDto,
} from './dto/catalog-product.dto';
import {
  FileFieldsInterceptor,
  FileInterceptor,
  FilesInterceptor,
} from '@nestjs/platform-express';
import { ApiKeyGuard } from 'src/guards/api-key.guards';
import { ValidateHeadersGuard } from 'src/guards/validate-header.gueard';
import { SortOrderValidationPipe } from 'src/pipes/sort-order-validation.pipe';
import { FilterProductsDto } from './dto/filter-product.dto';
import { ListProductsQueryDto } from './dto/list-products.dto';
import { ListProductsTransformerHelper } from './helpers/list-products-transformer.helper';
import { NamedNode } from 'ts-morph';
import { ProductHelperService } from './product-helper.service';

// @UseGuards(ApiKeyGuard)
@Controller('v1/catalog-admin')
export class CatalogProductController {
  constructor(
    private readonly catalogProductService: CatalogProductService,
    private readonly productHelperService: ProductHelperService,
  ) {}

  //CRUD FOR PRODUCT ATTRIBUTES
  @Post('/product-attribute')
  async createAttributes(
    @Body()
    body: CreateCatalogProductAttributesDto,
  ) {
    return this.catalogProductService.addProductAttributes(body);
  }

  @Get('/product-attribute')
  async getAllAttributes() {
    return this.catalogProductService.getAllProductAttributes();
  }

  @Patch('/product-attribute')
  async editProductAttributes(
    @Body()
    body: UpdateCatalogProductAttributesDto,
  ) {
    return this.catalogProductService.updateProductAttribute(body);
  }

  @Delete('product-attribute/:id')
  async deleteEvent(@Param('id') id: number) {
    return await this.catalogProductService.deleteProductAttribute(id);
  }

  //CRUD FOR MAIN CATALOG PRODUCT
  @Post('product')
  // @UseGuards(ValidateHeadersGuard)
  async createProduct(
    @Body()
    body: CreateCatalogProductDto,
    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    return this.catalogProductService.createCatalogProduct(body, headers);
  }

  // @Delete('product/:id')
  // async deleteCatalogProduct(@Param('id') id: number) {
  //   return await this.catalogProductService.deleteCatalogProduct(id);
  // }

  // @Delete('products')
  // async deleteMultipleCatalogProducts(@Body() body: { product_ids: number[] }) {
  //   return await this.catalogProductService.deleteMultipleCatalogProducts(
  //     body.product_ids,
  //   );
  // }

  @Patch('products')
  @UseGuards(ValidateHeadersGuard)
  async updateMultipleProductsStatus(
    @Body()
    body: UpdateMultipleProductsStatusDto,

    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    return await this.catalogProductService.updateMultipleProductsStatus(
      body.product_ids,
      body.newStatus,
      headers,
    );
  }

  @Post('search-products')
  async findProductsByIds(
    @Body()
    body: {
      product_ids: number[];
      filters: any;
      search_by_keyword: any;
      pagination: any;
      sort_by: any;
    },
  ) {
    return this.catalogProductService.getProductsByIds(
      body.product_ids,
      body.filters,
      body.search_by_keyword,
      body.pagination,
      body.sort_by,
    );
  }
  @Post('search-child-products')
  async findChildProductsByIds(
    @Body()
    body: {
      id: number;
      page: number;
      size: number;
    },
  ) {
    return this.catalogProductService.getChildProductsByIds(
      body.id,
      body.page,
      body.size,
    );
  }

  @Post('update/group-product-position')
  @UseGuards(ValidateHeadersGuard)
  async updateGroupProductPosition(
    @Body()
    body: {
      id: number;
      child_id: number;
      position: number;
    },
    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    return this.catalogProductService.updateGroupProductPosition(
      body.id,
      body.child_id,
      body.position,
      headers,
    );
  }
  @Patch('product/:id')
  @UseGuards(ValidateHeadersGuard)
  async updateProduct(
    @Body()
    body: UpdateCatalogProductDto,
    @Param('id') id: number,
    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    return this.catalogProductService.updateCatalogProduct(body, id, headers);
  }

  //CONTROLLER TO FETCH ATTRIBUTES BASED ON TYPE ID
  @Get('/attributes-type')
  async getAttributesTypewise() {
    return this.catalogProductService.getAttributesTypewise();
  }
  //CONTROLLERS FOR ATTRIBUTE GROUPS
  @Post('/attributes-group')
  async createAttributesGroup(
    @Body()
    body: CreateAttributesGroupDto,
  ) {
    return this.catalogProductService.createAttributesGroup(body);
  }

  @Get('/attributes-group')
  async getAttributesGroupList() {
    return this.catalogProductService.getAttributesGroupList();
  }

  @Patch('/attributes-group')
  async updateAttributesGroup(
    @Body()
    body: UpdateAttributesGroupDto,
  ) {
    return this.catalogProductService.updateAttributesGroup(body);
  }

  @Post('handle-media/:productId')
  @UseGuards(ValidateHeadersGuard)
  @UseInterceptors(FilesInterceptor('files'))
  async uploadFiles(
    @UploadedFiles() files: Express.Multer.File[],
    @Body() mediaData: any,
    @Param('productId', ParseIntPipe) productId: number,
    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    return await this.catalogProductService.handleMedia(
      files,
      productId,
      mediaData,
      headers,
    );
  }

  @Post('/attributes-group-relation')
  async addDataInAttributesGroupRelation(
    @Body()
    body: CreateAttributesGroupRelationDto,
  ) {
    return this.catalogProductService.addDataInAttributesGroupRelation(body);
  }

  @Get('/attributes-group-relation')
  async getAttributeGroupRelations(@Query('groupId') groupId?: number) {
    return this.catalogProductService.getAttributeRelationsData(groupId);
  }

  @Patch('/attributes-group-relation/:attributeId/:newSortOrder')
  async updateAttributeGroupRelations(
    @Param('attributeId') attributeId: number,
    @Param('newSortOrder') newSortOrder: number,
  ) {
    return this.catalogProductService.updateAttributeRelationsData(
      attributeId,
      newSortOrder,
    );
  }

  @Delete('/attributes-group-relation/:attributeId')
  async deleteAttributeGroupRelation(
    // @Param('groupId') groupId: number,
    @Param('attributeId') attributeId: number,
  ) {
    await this.catalogProductService.deleteAttributeGroupRelation(attributeId);
    return { message: 'Attribute group relation deleted successfully' };
  }

  //ATTRIBUTE OPTIONS ROUTES
  @Post('/attributes-options')
  async createAttributeOptions(
    @Body()
    body: {
      attribute_id: number;
      attribute_value: string[];
    },
  ) {
    return this.catalogProductService.createAttributeOptions(
      body.attribute_id,
      body.attribute_value,
    );
  }

  @Get('/attributes-options')
  async getAttributeOptions(
    @Query('attributeId') attributeId?: number,
    @Query('sortOrder', new SortOrderValidationPipe())
    sortOrder: 'ASC' | 'DESC' = 'ASC',
  ) {
    return this.catalogProductService.getAttributeOptionsList(
      attributeId,
      sortOrder,
    );
  }

  @Patch('/attributes-options')
  async updateAttributeOptions(
    @Body()
    body: {
      options_id: number;
      attribute_value: string;
    },
  ) {
    return this.catalogProductService.updateAttributeOptions(
      body.options_id,
      body.attribute_value,
    );
  }

  @Delete('/attributes-options/:optionsId')
  async deleteAttributeOptions(@Param('optionsId') optionsId: number) {
    return this.catalogProductService.deleteAttributeOptions(optionsId);
  }

  @Post('/bulk-update-attributes')
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, cb) => {
        if (!file.originalname.match(/\.(csv)$/)) {
          return cb(
            new BadRequestException('Only CSV files are allowed'),
            null,
          );
        }
        cb(null, true);
      },
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, cb) => {
          const filename = path.parse(file.originalname).name;
          const extension = path.parse(file.originalname).ext;
          cb(null, `${filename}${extension}`);
        },
      }),
    }),
  )
  @UseGuards(ValidateHeadersGuard)
  async bulkUpdateAttributesviaCSV(
    @UploadedFile() file: Express.Multer.File,
    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    try {
      const responseMessage = {
        message: 'Bulk update for product attributes is under process',
      };

      // const parsedUserMetaInfo = userMetaInfo ? JSON.parse(userMetaInfo) : null;

      // Use setImmediate for non-blocking processing with proper error handling
      setImmediate(async () => {
        try {
          const filePath = `./uploads/${file.originalname}`;
          await fs.promises.access(filePath, fs.constants.F_OK);
          const result = await this.catalogProductService.bulkUpdateAttributesviaCSV(
            filePath,
            file,
            headers,
          );

          if (result && result.status === 'error') {
            console.error('CSV processing completed with errors:', result.message);
          } else {
            console.log('CSV processing completed successfully');
          }
        } catch (error) {
          // Log the error but don't throw - this prevents server crashes
          console.error('Error processing CSV file (async):', error.message, error);

          // Additional logging for debugging
          if (error.stack) {
            console.error('Error stack:', error.stack);
          }
        }
      });

      return responseMessage;
    } catch (error) {
      console.error('Error processing CSV file:', error);
      throw new Error('Failed to process CSV file');
    }
  }

  @Get('attribute-groupwise-detail/:id')
  async productDetailsAttributeGroupWise(@Param('id') id: number) {
    return this.catalogProductService.productDetailsAttributeGroupWise(id);
  }

  @Post('/download-product-csv')
  async downloadProductCsv(
    @Body()
    body: {
      product_ids?: number[];
      filters?: any;
      columns_list: string[];
    },
  ) {
    try {
      if (!body.product_ids && Object.keys(body?.filters).length === 0) {
        throw new BadRequestException('No products provided to generate CSV');
      }
      const responseMessage = {
        message: 'Data export is under process please check after some time',
      };
      setImmediate(async () => {
        try {
          await this.catalogProductService.downloadProductCsv(
            body.product_ids,
            body.filters,
            body.columns_list,
          );
        } catch (error) {
          console.error('Error processing fetch product CSV file:', error);
          throw new Error('Error processing fetch product CSV file');
        }
      });
      return responseMessage;
    } catch (error) {
      console.log(error);
      if (error.status === 400) {
        throw error;
      } else {
        throw new InternalServerErrorException(
          'Failed to generate bulk products data csv',
        );
      }
    }
  }

  @Get('/bulk-data-csv-list')
  findAll(@Query('page_no', ParseIntPipe) page_no: number) {
    return this.catalogProductService.bulkDataCsvList(page_no);
  }

  @Get('/attributes-update-csv-list')
  findAllList(@Query('page_no', ParseIntPipe) page_no: number) {
    return this.catalogProductService.bulkAttributeUpdateCsvList(page_no);
  }

  @Post('/bulk-update-sku')
  @UseGuards(ValidateHeadersGuard)
  async bulkUpdateSkus(
    @Body()
    body: {
      sku_list: string[];
    },
    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    try {
      const responseMessage = {
        message: 'Bulk update for SKUs is under process',
      };

      // const parsedUserMetaInfo = userMetaInfo ? JSON.parse(userMetaInfo) : null;

      setImmediate(async () => {
        try {
          return await this.catalogProductService.bulkUpdateSkus(body, headers);
        } catch (error) {
          console.error('Error processing update SKU list', error);
          throw new Error('Failed to process update SKU list');
        }
      });

      return responseMessage;
    } catch (error) {
      console.error('Error processing update SKU list', error);
      throw new InternalServerErrorException(
        'Failed to process update SKU list',
      );
    }
  }

  @Get('products/:productId/activity-logs')
  async getProductsLogsDetails(
    @Param('productId', ParseIntPipe) productId: number,
    @Query('page') page: number,
    @Query('size') size: number,
  ) {
    try {
      const pagination = {
        size: size,
        page: page,
      };

      return await this.catalogProductService.generateProductLogs(
        productId,
        pagination,
      );
    } catch (error) {
      console.log(error);
      throw new Error('Failed to generate product logs');
    }
  }

  @Post('products/activity-log/:logId/revert')
  @UseGuards(ValidateHeadersGuard)
  async revertActivityLog(
    @Param('logId', ParseIntPipe) id: number,
    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    return this.catalogProductService.revertActivityLog(id, headers);
  }

  @Post('elastic-search-crons')
  async elsaticSearchCronJob() {
    return this.catalogProductService.elsaticSearchCronJob();
  }

  @Patch('update-outbox-table')
  async updateOutboxTable(
    @Body()
    body: {
      outbox_table_ids: number[];
    },
  ) {
    return this.catalogProductService.updateOutboxTable(body.outbox_table_ids);
  }


  @Get('list-products')
  async listProducts(
    @Query(new ValidationPipe({ transform: true }))
    queryDto: ListProductsQueryDto,
  ) {
    // Transform query parameters to request body format
    const request_body = ListProductsTransformerHelper.transformQueryToRequestBody(queryDto);

    // Validate the transformed request body
    const validation = ListProductsTransformerHelper.validateRequestBody(request_body);
    if (!validation.isValid) {
      throw new BadRequestException({
        message: 'Invalid request parameters',
        errors: validation.errors,
      });
    }

    // Log transformation for debugging (can be removed in production)
  
    return this.catalogProductService.listProducts(request_body);
  }
  // @Get('list-products-v2')
  // async listProductsV2(
  //   @Query(new ValidationPipe({ transform: true }))
  //   filterDto: ListProductsV2Dto,
  // ) {
  //   console.log("filterDto", JSON.stringify(filterDto, null, 2));
  //   return this.catalogProductService.listProductsV2(filterDto);
  // }

  @Patch('activity-logs')
  async updateActivityLogs(
    @Body()
    body: {
      outbox_table_ids: number[];
    },
  ) {
    return this.catalogProductService.updateOutboxTable(body.outbox_table_ids);
  }

  // @Put('products/:sku/inventory')
  // async viniculumInventoryUpdate(
  //   @Param('sku') sku: string,
  //   @Body()
  //   body: {
  //     inventoryItem: ViniculumInventoryItemDto;
  //   },
  // ) {
  //   return this.catalogProductService.viniculumInventoryUpdate(
  //     sku,
  //     body.inventoryItem,
  //   );
  // }

  @Post('group_tierprice_update/:productId/')
  @UseGuards(ValidateHeadersGuard)
  async updateGroupTierPrice(
    @Param('productId', ParseIntPipe) productId: number,
    @Body()
    body: {
      group_tier_price;
    },
    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    return this.catalogProductService.updateGroupTierPrice(
      productId,
      body.group_tier_price,
      headers,
    );
  }

  @Get('video-info')
  async videoUrlInfo(@Query('url') url: string) {
    return this.catalogProductService.fetchYoutubeVideoInfo(url);
  }

  @Post('save-video-media/:productId')
  async saveYoutubeVideo(
    @Body()
    body: {
      title: string;
      description: string;
      thumbnail: string;
      url: string;
    },
    @Param('productId', ParseIntPipe) productId: number,
    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    return this.catalogProductService.saveProductsYoutubeVideoInfo(
      body,
      productId,
      headers,
    );
  }

  @Get('list-sku-updates')
  async listUpdatedSkus(
    @Query('old_sku') old_sku?: string,
    @Query('filters_status') filters_status?: boolean,
    @Query('filters_created_at_from') filters_created_at_from?: string,
    @Query('filters_created_at_to') filters_created_at_to?: string,
    @Query('filters_updated_at_from') filters_updated_at_from?: string,
    @Query('filters_updated_at_to') filters_updated_at_to?: string,
    @Query('new_sku') new_sku?: string,
    @Query('page') page?: number,
    @Query('size') size?: number,
  ) {
    //NOTE: send status as 0 or 1 for success and failure respectively
    let requestBody = {
      new_sku,
      old_sku,
      filters: {
        ...(filters_status !== undefined && { status: filters_status }),
        created_at: {
          from: filters_created_at_from,
          to: filters_created_at_to,
        },
        updated_at: {
          from: filters_updated_at_from,
          to: filters_updated_at_to,
        },
      },
      pagination: {
        page,
        size,
      },
    };

    return this.productHelperService.listUpdatedSkus(requestBody);
  }

  @Get('updated-skus-csv')
  async downloadUpdatedSkuCsv(
    @Res() res: Response,
    @Query('old_sku') old_sku?: string,
    @Query('filters_status') filters_status?: boolean,
    @Query('filters_created_at_from') filters_created_at_from?: string,
    @Query('filters_created_at_to') filters_created_at_to?: string,
    @Query('filters_updated_at_from') filters_updated_at_from?: string,
    @Query('filters_updated_at_to') filters_updated_at_to?: string,
    @Query('new_sku') new_sku?: string,
    @Query('page') page?: number,
    @Query('size') size?: number,
  ) {
    try {
      //NOTE: send status as 0 or 1 for success and failure respectively
      let requestBody = {
        new_sku,
        old_sku,
        filters: {
          ...(filters_status !== undefined && { status: filters_status }),
          created_at: {
            from: filters_created_at_from,
            to: filters_created_at_to,
          },
          updated_at: {
            from: filters_updated_at_from,
            to: filters_updated_at_to,
          },
        },
        pagination: {
          page,
          size,
        },
      };

      const filename =
        await this.productHelperService.downloadskuUpdateListCsv(requestBody);

      const filepath = path.join('./reports', filename);

      if (!fs.existsSync(filepath)) {
        throw new InternalServerErrorException('File not found');
      }

      res.download(filepath, filename, (err) => {
        if (err) {
          console.error('File download error:', err);
          res.status(500).send('Failed to download file.');
        } else {
          // Optional: Clean up file if needed after download
          fs.unlink(filepath, (err) => {
            if (err) {
              console.error('Error deleting file:', err);
            }
          });
        }
      });
    } catch (error) {
      console.error('Error generating or sending file:', error);

      if (error.status === 400) {
        throw error;
      } else {
        throw new InternalServerErrorException(
          'Failed to generate or send file',
        );
      }
    }
  }

  @Post('stock-alert/:productId')
  async createProductStockAlert(
    @Param('productId', ParseIntPipe) productId: number,
    @Headers()
    headers: {
      'admin-identifier': string;
      platform?: string;
      user_agent?: string;
      Authorization: string;
    },
  ) {
    return this.productHelperService.createProductStockAlert(
      productId,
      headers,
    );
  }

  @Get('stock-alert')
  async listProductStockAlert(
    @Query('sku') sku?: string,
    @Query('product_name') product_name?: string,
    @Query('customer_name') customer_name?: string,
    @Query('customer_email') customer_email?: string,
    // @Query('filters_status') filters_status?: boolean,
    @Query('filters_created_at_from') filters_created_at_from?: string,
    @Query('filters_created_at_to') filters_created_at_to?: string,
    @Query('page') page?: number,
    @Query('size') size?: number,
  ) {
    let requestBody = {
      sku,
      product_name,
      customer_name,
      customer_email,
      created_at: {
        from: filters_created_at_from,
        to: filters_created_at_to,
      },
      pagination: {
        page,
        size,
      },
    };

    return this.productHelperService.listStockAlertData(requestBody);
  }

  @Get('stock-alert-csv')
  async downloadProductStockAlertCsv(
    @Res() res: Response,
    @Query('sku') sku?: string,
    @Query('product_name') product_name?: string,
    @Query('customer_name') customer_name?: string,
    @Query('customer_email') customer_email?: string,
    // @Query('filters_status') filters_status?: boolean,
    @Query('filters_created_at_from') filters_created_at_from?: string,
    @Query('filters_created_at_to') filters_created_at_to?: string,
    @Query('page') page?: number,
    @Query('size') size?: number,
  ) {
    try {
      let requestBody = {
        sku,
        product_name,
        customer_name,
        customer_email,
        created_at: {
          from: filters_created_at_from,
          to: filters_created_at_to,
        },
        pagination: {
          page,
          size,
        },
      };

      const filename =
        await this.productHelperService.downloadStockAlertListCsv(requestBody);

      const filepath = path.join('./reports', filename);

      if (!fs.existsSync(filepath)) {
        throw new InternalServerErrorException('File not found');
      }

      res.download(filepath, filename, (err) => {
        if (err) {
          console.error('File download error:', err);
          res.status(500).send('Failed to download file.');
        } else {
          // Optional: Clean up file if needed after download
          fs.unlink(filepath, (err) => {
            if (err) {
              console.error('Error deleting file:', err);
            }
          });
        }
      });
    } catch (error) {
      console.error('Error generating or sending file:', error);

      if (error.status === 400) {
        throw error;
      } else {
        throw new InternalServerErrorException(
          'Failed to generate or send file',
        );
      }
    }
  }
}

// Below class is just for a specific route which we need for viniculum
@UseGuards(ApiKeyGuard)
@Controller('catalog/admin-api/v1')
export class CatalogProductControllerForViniculum {
  constructor(
    private readonly catalogProductService: CatalogProductService,
    private readonly productHelperService: ProductHelperService,
  ) {}

  @Put('products/:sku/inventory')
  async viniculumInventoryUpdate(
    @Param('sku') sku: string,
    @Body()
    body: {
      inventoryItem: ViniculumInventoryItemDto;
    },
  ) {
    //  return { message: 'Inventory details updated successfully' };
    return this.catalogProductService.viniculumInventoryUpdate(
      sku,
      body.inventoryItem,
    );
  }

  @Get('list-products')
  async getFilteredProducts(
    @Query(new ValidationPipe({ transform: true }))
    filterDto: FilterProductsDto,
  ) {
    return this.productHelperService.getFilteredProducts(filterDto);
  }
}
