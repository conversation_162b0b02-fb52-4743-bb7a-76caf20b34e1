import {
  BadGatewayException,
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CatalogCategory } from 'src/database/entities/category/main-category.entity';
import { CategoryBanner } from 'src/database/entities/category/category-banner.entity';
import {
  DataSource,
  In,
  Or,
  Repository,
  Like,
  EntityManager,
  Not,
  QueryRunner,
} from 'typeorm';
import {
  CreateCatalogCategoryDto,
  UpdateCatalogCategoryDto,
} from './dto/catalog-category.dto';
import {
  CreateCategoryBannerDto,
  UpdateCategoryBannerDto,
  CategoryBannerQueryDto,
} from './dto/category-banner.dto';
import { ProductCategoryRelation } from 'src/database/entities/category/product-category-relation.entity';
import { CatalogProductService } from '../catalog-product/catalog-product.service';
import env from 'src/config/env';
import { ProductAttributeName } from 'src/database/entities/product/product-attribute-name.entity';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { S3Service } from 'src/utils/s3service';
import { LoggerService } from 'src/utils/logger-service';
import { ExternalApiHelper } from 'src/utils/external-api-helper';
import {
  EntityTypeEnum,
  UrlRewrites,
} from 'src/database/entities/product/url-rewrites.entity';
import { EventsLogService } from '../../../utils/events-logging-service';
import { EntityType } from 'src/database/entities/outbox/event-outbox.entity';
import { BrandsService } from '../brands/brands.service';
import { Activity } from 'src/database/entities/product/activity.entity';
import { ActivityLogs } from 'src/database/entities/product/activity-logs.entity';

@Injectable()
export class CatalogCategoryService {
  constructor(
    private catalogProductService: CatalogProductService,
    @InjectRepository(CatalogCategory)
    private readonly categoryRepository: Repository<CatalogCategory>,
    @InjectRepository(ProductCategoryRelation)
    private readonly productCategoryRelationRepository: Repository<ProductCategoryRelation>,
    @InjectRepository(CatalogProduct)
    private readonly catalogProductRepository: Repository<CatalogProduct>,
    @InjectRepository(ProductAttributeName)
    private readonly productAttributesNameRepository: Repository<ProductAttributeName>,
    @InjectRepository(UrlRewrites)
    private readonly urlRewritesRepository: Repository<UrlRewrites>,
    @InjectRepository(CategoryBanner)
    private readonly categoryBannerRepository: Repository<CategoryBanner>,
    @InjectRepository(Activity)
    private readonly activityRepository: Repository<Activity>,
    @InjectRepository(ActivityLogs)
    private readonly activityLogsRepository: Repository<ActivityLogs>,
    private readonly s3Service: S3Service,
    private readonly logger: LoggerService,
    private readonly entityManager: EntityManager,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly eventsLogService: EventsLogService,
    @Inject(forwardRef(() => BrandsService))
    private readonly brandsService: BrandsService,
  ) {}

  // create category
  async createCatalogCategory(
    body: CreateCatalogCategoryDto,
    headers: Record<string, string | string[]>,
  ) {
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const { inputData, category } = await this.createCatgory(
        body,
        queryRunner,
        headers,
      );
      await queryRunner.commitTransaction();
      return {
        category: inputData,
        nonExistingProducts: [],
        savedCategory: category,
      };
    } catch (err) {
      this.logger.error('Failed to create category', err);
      await queryRunner.rollbackTransaction();
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException('Failed to create category');
      }
    } finally {
      await queryRunner.release();
    }
  }

  private async generateUniqueCategoryName(name: string): Promise<string> {
    const baseName = this.sanitize(name);

    let newName = baseName;
    let count = 1;

    // Ensure the name is unique
    while (await this.isCategoryNameExists(newName)) {
      newName = `${baseName}-${count}`;
      count++;
    }

    return newName;
  }

  private sanitize(name: string) {
    if (!name || typeof name !== 'string') {
      throw new Error('Invalid category name');
    }

    // Normalize Unicode and replace diacritics with plain Latin characters
    let baseName = name.normalize('NFD').replace(/[\u0300-\u036f]/g, '');

    // Remove newlines and tabs
    baseName = baseName.replace(/[\n\t]/g, '');

    // Normalize the name: lowercase, trim, replace multiple spaces/special characters with single hyphens
    baseName = baseName
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, '') // Allow hyphens along with alphanumeric characters and spaces
      .trim()
      .replace(/\s+/g, '-'); // Replace spaces (including multiple) with a single hyphen

    // Remove non-Latin characters
    baseName = baseName.replace(/[^\w\s-]/g, ''); // Keep alphanumeric, space, and hyphen

    return baseName;
  }

  private async isCategoryNameExists(name: string): Promise<boolean> {
    const existingCategory = await this.categoryRepository.findOne({
      where: { url_key: name },
    });
    return !!existingCategory;
  }

  async updateCategory(
    body: UpdateCatalogCategoryDto,
    file: Express.Multer.File,
    categoryId: number,
    headers: Record<string, string | string[]>,
  ) {
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const existingCategory = await this.categoryRepository.findOne({
        where: { id: categoryId },
      });

      if (!existingCategory) {
        throw new BadRequestException('Requested Category does not exists');
      }

      if (body.name) {
        if (body.name?.length === 0) {
          throw new BadRequestException('Cateory name cannot be empty');
        }

        const existingCategoryNameForSameParentCategory =
          await this.categoryRepository.findOne({
            where: { name: body.name, parent_id: existingCategory.parent_id },
          });

        if (existingCategoryNameForSameParentCategory) {
          throw new BadRequestException(
            'A category with the same name already exists for the same parent category',
          );
        }
      }

      let { status, parent_id, ...updatedBody } = body;

      if(parent_id && Number(parent_id) === categoryId) {
        throw new BadRequestException('Parent category cannot be itself');
      }

      let booleanStatus, includeInMenuStatus;

      if (body?.status === 'true') {
        booleanStatus = true;
      } else if (body.status === 'false') {
        booleanStatus = false;
      }

      if (body?.include_in_menu == 'true') {
        includeInMenuStatus = true;
      } else if (body.include_in_menu == 'false') {
        includeInMenuStatus = false;
      }

      let integerParentId, url_rewrites_body;
      if (parent_id) {
        integerParentId = Number(parent_id);

        //special case Brands name can't be modified
        if (
          updatedBody.name &&
          existingCategory.parent_id === +env.dk.dental_brands_id
        ) {
          updatedBody.name = existingCategory.name;
        }
      }

      if (updatedBody.url_key !== undefined) {
        updatedBody.url_key = this.sanitize(updatedBody.url_key);

        const url_key_exists = await this.categoryRepository.findOne({
          where: { url_key: updatedBody.url_key },
        });
        const existingCategoryUrlInUrlRewrites =
          await this.urlRewritesRepository.findOne({
            where: { request_path: `${updatedBody.url_key}.html` },
          });
        if (url_key_exists || existingCategoryUrlInUrlRewrites) {
          this.logger.log("url_key_existsUrl key must be unique");
          
          throw new BadRequestException('Url key must be unique');
        } else {
          url_rewrites_body = {
            entity_id: existingCategory.id,
            entity_type: EntityTypeEnum.CATEGORY,
            request_path: `${updatedBody.url_key}.html`,
            target_path: String(existingCategory.id),
            old_request_path:
              body.enableRedirect === 'true'
                ? `${existingCategory.url_key}.html`
                : undefined,
          };

          const { oldUrlRewrite, updatedUrlRewrite, createdUrlRewrite } =
            await this.catalogProductService.modifyUrlRewritesV2(
              url_rewrites_body,
              queryRunner,
            );

          if (existingCategory.parent_id === +env.dk.dental_brands_id) {
            await this.brandsService.updateBrandUrlKey(
              categoryId,
              updatedBody.url_key,
              queryRunner,
            );
          }

          if (oldUrlRewrite) {
            await Promise.all([
              this.eventsLogService.saveActivityAndEvent({
                headers,
                entityId: oldUrlRewrite.id,
                entityType: EntityType.URL_REWRITE,
                activityEntityType: EntityTypeEnum.URL_REWRITE,
                activityType: 'modify-category-url-rewrite',
                queryRunner: queryRunner,
                updatedValue: updatedUrlRewrite,
                previousValue: oldUrlRewrite,
              }),
              this.eventsLogService.saveActivityAndEvent({
                headers,
                entityId: createdUrlRewrite.id,
                entityType: EntityType.URL_REWRITE,
                activityEntityType: EntityTypeEnum.URL_REWRITE,
                activityType: 'create-category-url-rewrite',
                queryRunner: queryRunner,
                updatedValue: null,
                previousValue: null,
              }),
            ]);
          } else {
            await this.eventsLogService.saveActivityAndEvent({
              headers,
              entityId: createdUrlRewrite.id,
              activityEntityType: EntityTypeEnum.URL_REWRITE,
              entityType: EntityType.URL_REWRITE,
              activityType: 'create-category-url-rewrite',
              queryRunner: queryRunner,
              updatedValue: null,
              previousValue: null,
            });
          }
        }
      }

      delete updatedBody.enableRedirect;
      const updateObject: any = { ...updatedBody };
      if (booleanStatus !== undefined) {
        updateObject.status = booleanStatus;
      }
      if (integerParentId !== undefined) {
        updateObject.parent_id = integerParentId;
      }

      if (includeInMenuStatus !== undefined) {
        updateObject.include_in_menu = includeInMenuStatus;
      }
      const updateDetails = await queryRunner.manager.update(
        CatalogCategory,
        { id: categoryId },
        { ...updateObject },
      );

      if (file) {
        let fileData = file;
        const fileName = await this.buildMediaImageName(fileData.originalname);
        fileData.originalname = fileName;
        const { image } = await this.uploadCategoryImage(
          [fileData],
          categoryId,
          queryRunner,
        );
        updateObject.image = image.split('.com').pop();
        
      }
      await this.eventsLogService.saveActivityAndEvent({
        headers,
        entityId: existingCategory.id,
        entityType: EntityType.CATEGORY,
        activityEntityType: EntityTypeEnum.CATEGORY,
        activityType: 'update-category',
        queryRunner: queryRunner,
        updatedValue: updateObject,
        previousValue: existingCategory,
      });

      await queryRunner.commitTransaction();

      // if (body.url_key && url_rewrites_body.old_request_path !== undefined) {
      //   Promise.all([
      //     this.catalogProductService.syncUrlRewriteswithElasticSearch(
      //       url_rewrites_body.request_path,
      //     ),

      //     this.catalogProductService.syncUrlRewriteswithElasticSearch(
      //       url_rewrites_body.old_request_path,
      //     ),
      //   ]);
      // } else if (
      //   body.url_key &&
      //   url_rewrites_body.old_request_path === undefined
      // ) {
      //   this.catalogProductService.syncUrlRewriteswithElasticSearch(
      //     url_rewrites_body.request_path,
      //   );
      // }

      // await this.syncCategorywithElasticSearch(categoryId);

      // await this.syncCategorywithMongo(categoryId);

      const updatedCategoryDetails = await this.categoryRepository.findOne({
        where: { id: categoryId },
      });


      return {
        message: 'Category updated successfuly',
        categoryData: updatedCategoryDetails,
      };
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to update category data', err);
      await queryRunner.rollbackTransaction();
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException(
          'Failed to update category data',
        );
      }
    } finally {
      await queryRunner.release();
    }
  }

  private async buildMediaImageName(name: string): Promise<string> {
    let nameParts = name.split('.');
    let fileName = nameParts[0];
    let extension = nameParts[1];
    let imageName = `${env.aws.bucketBaseUrl}${fileName}.${extension}`;
    let count = 1;

    while (await this.isMediaImageNameExists(imageName)) {
      imageName = `${env.aws.bucketBaseUrl}${fileName}_${count}.${extension}`;
      count++;
    }

    return imageName.split('/').pop();
  }

  private async isMediaImageNameExists(name: string): Promise<boolean> {
    const existingImage = await this.categoryRepository.findOne({
      where: { image: name },
    });
    return !!existingImage;
  }

  // get all category
  async getAllCatalogCategory(page_no, category_name) {
    try {
      let searchBody: any = {};

      searchBody['skip'] = ((page_no || 1) - 1) * env.sqlQueryResultsize;
      searchBody['take'] = env.sqlQueryResultsize;

      if (category_name) {
        searchBody.where = { name: Like(`%${category_name}%`), id: Not(1) };
      } else {
        searchBody.where = {
          id: Not(1),
        };
      }

      const allCatalogCategory =
        await this.categoryRepository.findAndCount(searchBody);

      let catalogCategoriesArray = allCatalogCategory[0];
      let catalogCategoriesCount = allCatalogCategory[1];

      return {
        item_count: catalogCategoriesCount,
        page_no: page_no,
        page_size: catalogCategoriesArray.length,
        items: catalogCategoriesArray,
      };
    } catch (error) {
      this.logger.error('Failed to retrieve catalog categories', error);

      throw new InternalServerErrorException(
        'Failed to retrieve catalog categories',
      );
    }
  }

  async getCategoryById(
    category_ids?: number[],
    filters?: { name: string },
    pagination?: { page: number },
  ) {
    try {
      const mainCatalogQuery: any = {};

      if (category_ids && category_ids.length > 0) {
        mainCatalogQuery.where = { id: In(category_ids) };
      } else if (filters && filters.name) {
        mainCatalogQuery.where = { name: Like(`%${filters.name}%`) };
      } else {
        mainCatalogQuery.where = {};
        mainCatalogQuery.order = { id: 'DESC' };
      }

      if (pagination) {
        mainCatalogQuery.skip = (pagination.page - 1) * env.sqlQueryResultsize;
        mainCatalogQuery.take = env.sqlQueryResultsize;
      }
      const categoryData = await this.categoryRepository.find(mainCatalogQuery);

      return categoryData;
    } catch (error) {
      this.logger.error('Failed to retrieve catalog categories', error);

      throw new InternalServerErrorException(
        'Failed to retrieve catalog categories',
      );
    }
  }

  async getProductsInCategory(
    categoryId: number,
    filters?: any,
    pagination?: any,
  ) {
    try {
      const categoryExists = await this.categoryRepository.findOne({
        where: { id: categoryId },
      });

      if (!categoryExists) {
        throw new BadRequestException('Invalid Category');
      }

      let queryOptions: any = {
        relations: [],
        where: {},
      };
      var selectClause: any = {};

      queryOptions.relations = [
        'product.inventoryAttributesRelations',
        'product.catalogProductFlatRelations',
        // 'product.booleanAttributeValuesRelations.attribute',
        // 'product.stringAttributeValuesRelations.attribute',
        // 'product.integerAttributeValuesRelations.attribute',
        // 'product.decimalAttributeValuesRelations.attribute',
      ];

      queryOptions.where.category = { id: categoryId };

      selectClause = {
        id: true,
        position: true,
        product: {
          id: true,
          sku: true,
          type_id: true,
          status: true,
          inventoryAttributesRelations: { is_in_stock: true },
          catalogProductFlatRelations: {
            id: true,
            product: true,
            visibility: true,
            international_active: true,
            name: true,
            price: true,
            special_price: true,
            thumbnail: true,
          },
        },
      };

      if (filters) {
        if(filters.name) {
          queryOptions.where.product = {
          ...(queryOptions.where.product || {}),
            catalogProductFlatRelations: {
              name: Like(`%${filters.name}%`),
            },
          };
        }
        if (filters.type_id) {
          queryOptions.where.product = {
            ...(queryOptions.where.product || {}),
            type_id: filters.type_id,
          };
        }
        if (filters.status !== undefined) {
          queryOptions.where.product = {
            ...(queryOptions.where.product || {}),
            status: filters.status,
          };
        }
        if (filters.stock_status !== undefined) {
          queryOptions.where.product = {
            ...(queryOptions.where.product || {}),
            inventoryAttributesRelations: {
              is_in_stock: filters.stock_status,
            },
          };
        }

        if (filters.international_active) {
          queryOptions.where.product = {
            ...(queryOptions.where.product || {}),

            catalogProductFlatRelations: {
              international_active: filters.international_active,
            },
          };
        }
      }

      queryOptions['skip'] =
        ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
      queryOptions['take'] = pagination?.size
        ? pagination?.size
        : env.sqlQueryResultsize;

      // console.log(JSON.stringify(queryOptions), 'QO');

      const categoryData =
        await this.productCategoryRelationRepository.findAndCount({
          ...queryOptions,
          select: selectClause,
          order: { position: 'ASC NULLS LAST' },
        });
      const data = this.category_products_data_mapper(categoryData[0]);

      return {
        item_count: categoryData[1],
        pages_count: Math.ceil(categoryData[1] / env.sqlQueryResultsize),
        page_no: pagination?.page,
        page_size: data.length,
        items: data,
      };
    } catch (error) {
      this.logger.error(
        'Failed to retrieve products inside respective categories',
        error,
      );

      throw new InternalServerErrorException(
        'Failed to retrieve products inside respective categories',
      );
    }
  }

  // add products from category

  async addProductsToCategory(
    categoryId: number,
    product_ids: { id: number; position: number }[],
    headers: Record<string, string | string[]>,
  ) {
    try {
      const productsToAdd: { id: number; position: number }[] = [];

      const existingProducts =
        await this.catalogProductService.catalogProductFindById(
          product_ids.map((product) => product.id),
        );

      if (!existingProducts.length)
        return {
          addedProducts: [],
          notFoundProducts: product_ids,
        };

      const category = await this.categoryRepository.findOne({
        where: { id: categoryId },
      });

      if (!category)
        throw new HttpException(
          'Category does not exist',
          HttpStatus.BAD_REQUEST,
        );

      const productsInCategories =
        await this.productCategoryRelationRepository.find({
          where: { category: { id: categoryId } },
          relations: ['product'],
        });

      const productIdsInCategories = productsInCategories.map((e) => {
        return e.product.id;
      });

      for (const product of product_ids) {
        const exists = existingProducts.some(
          (existingProduct) => existingProduct.id === product.id,
        );

        if (exists && productIdsInCategories.indexOf(product.id) == -1) {
          productsToAdd.push(product);
        }
      }

      const notFoundProducts = product_ids.filter(
        (product) =>
          !productsToAdd.some(
            (existingProduct) => existingProduct.id === product.id,
          ),
      );

      if (!productsToAdd.length) {
        return { removedProducts: [], notFoundProducts };
      }

      const relationsToAdd = productsToAdd.map((product) => {
        const relation = new ProductCategoryRelation();
        relation.category = category;
        relation.product = existingProducts.find(
          (existingProduct) => existingProduct.id === product.id,
        );
        relation.position = product.position || null;
        return relation;
      });

      await this.categoryProductUpdateTransactionBlock(
        category,
        relationsToAdd,
        headers,
        productIdsInCategories,
        [...productsToAdd.map((o) => o.id), ...productIdsInCategories],
      );

      // await this.syncCategorywithElasticSearch(categoryId);
      // await this.syncCategorywithMongo(categoryId);

      return { addedProducts: productsToAdd, notFoundProducts };
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST) {
        throw error;
      }
      this.logger.error('Failed to add products inside category', error);

      throw new InternalServerErrorException(
        'Failed to add products inside categories',
      );
    }
  }

  // removeProductsfromCategory
  // async removeProductsFromCategory(categoryId: number, productIds: number[]) {
  //   try {
  //     const relationsToDelete =
  //       await this.productCategoryRelationRepository.find({
  //         where: {
  //           category: { id: categoryId },
  //           product: In(productIds),
  //         },
  //         relations: ['product'],
  //       });

  //     const productsToRemove = relationsToDelete.map(
  //       (relation) => relation.product.id,
  //     );

  //     await this.productCategoryRelationRepository.remove(relationsToDelete);

  //     const notFoundProducts = productIds.filter(
  //       (productId) => !productsToRemove.includes(productId),
  //     );

  //     // await this.syncCategorywithElasticSearch(categoryId);

  //     await this.syncCategorywithMongo(categoryId);

  //     return { removedProducts: productsToRemove, notFoundProducts };
  //   } catch (error) {
  //     this.logger.error('Failed to remove products from category', error);

  //     throw new InternalServerErrorException(
  //       'Failed to remove products from category',
  //     );
  //   }
  // }

  // delete category
  async deleteCatalogCategory(id: number) {
    // if (id === 1) {
    //   //root category
    //   throw new BadRequestException('cannot delete root id');
    // }
    const category = await this.categoryRepository.findOneBy({ id });

    if (!category) {
      throw new BadRequestException('Category not found');
    }

    await this.productCategoryRelationRepository.delete({ category });
    await this.categoryRepository.delete(id);

    return {
      id,
      response:
        'Catalog category and associated product-category relations deleted successfully',
    };
  }

  private async uploadCategoryImage(
    files: Express.Multer.File[],
    categoryId,
    queryRunner,
  ) {
    try {
      if (!files) {
        throw new BadRequestException('File is required');
      }

      const fileUrl = await this.s3Service.uploadFilesToS3(
        files,
        `${env.aws.categoryImagesFolderName}`,
      );
      const updateCategoryImage = await queryRunner.manager.update(
        CatalogCategory,
        { id: categoryId },
        { image: fileUrl[0].split('.com').pop() },
      );
      return {
        message: 'Category image uploaded successfully',
        image: fileUrl[0],
      };
    } catch (error) {
      this.logger.error('Failed to upload category image', error);

      throw new InternalServerErrorException('Failed to upload category image');
    }
  }

  async deleteCategoryImages(categoryId) {
    const findCategory = await this.categoryRepository.findOne({
      where: { id: categoryId },
    });

    if (!findCategory) {
      throw new BadRequestException('Invalid category Id');
    }

    let imageKey = findCategory.image.substring(
      findCategory.image.indexOf('.com/') + 5,
    );

    await this.s3Service.deleteFiles([imageKey]);

    const updateCategoryImage = await this.categoryRepository.update(
      { id: categoryId },
      { image: null },
    );

    return {
      message: 'S3 category image deleted successfully',
    };
  }

  //OLD CODE LOGIC
  // async getCategoryTree(): Promise<{ data: { categoryList: any[] } }> {
  //   try {
  //     const allCategories = await this.categoryRepository.find({
  //       order: { position: 'ASC' },
  //     });

  //     const topLevelCategories = allCategories.filter(
  //       (category) => category.parent_id === 2,
  //     );
  //     // .sort((a, b) => a.position - b.position);

  //     const categoryList = topLevelCategories.map(
  //       async (category) =>
  //         await this.buildCategoryTree(category, allCategories, [
  //           category.name,
  //         ]), // Limit recursion depth
  //     );

  //     const resolvedCategoryList = await Promise.all(categoryList);

  //     return { data: { categoryList: resolvedCategoryList } };
  //   } catch (error) {
  //     this.logger.error('Category Tree creation failed', error);
  //     throw new Error('Failed to fetch category tree');
  //   }
  // }

  // private async buildCategoryTree(
  //   parentCategory: CatalogCategory,
  //   allCategories: CatalogCategory[],
  //   path: string[],
  // ): Promise<any> {
  //   const children = allCategories.filter(
  //     (category) => category.parent_id === parentCategory.id,
  //   );
  //   // .sort((a, b) => a.position - b.position);

  //   if (children.length === 0) {
  //     return {
  //       name: parentCategory.name,
  //       id: parentCategory.id,
  //       position: parentCategory.position,
  //       url_path: parentCategory.url_key,
  //       children: [],
  //     };
  //   }

  //   const childrenData = await Promise.all(
  //     children.map((child) =>
  //       this.buildCategoryTree(child, allCategories, [...path, child.name]),
  //     ),
  //   );

  //   return {
  //     name: parentCategory.name,
  //     id: parentCategory.id,
  //     url_path: parentCategory.url_key,
  //     positon: parentCategory.position,
  //     children: childrenData,
  //     pathok: path.join('/'),
  //   };
  // }

  async getCategoryTree(): Promise<{ data: { categoryList: any[] } }> {
    try {
      const allCategories = await this.categoryRepository.find({
        order: { position: 'ASC' },
      });

      const topLevelCategories = allCategories.filter(
        (category) => category.parent_id === 2,
      );

      const categoryList = topLevelCategories.map(
        async (category) =>
          await this.buildCategoryTree(category, allCategories, [
            category.name,
          ]), // Start with the name of the top-level category
      );

      const resolvedCategoryList = await Promise.all(categoryList);
      // console.log("resolvedCategoryList", resolvedCategoryList)

      return { data: { categoryList: resolvedCategoryList } };
    } catch (error) {
      this.logger.error('Category Tree creation failed', error);
      throw new Error('Failed to fetch category tree');
    }
  }

  private async buildCategoryTree(
    parentCategory: CatalogCategory,
    allCategories: CatalogCategory[],
    path: string[], // Pass the path array through recursion
  ): Promise<any> {
    // Find children categories
    const children = allCategories.filter(
      (category) => category.parent_id === parentCategory.id,
    );

    // Recursively build tree for children and append their names to path
    const childrenData =
      children.length > 0
        ? await Promise.all(
            children.map((child) =>
              this.buildCategoryTree(child, allCategories, [
                ...path,
                child.name,
              ]),
            ),
          )
        : [];

    return {
      name: parentCategory.name,
      id: parentCategory.id,
      url_path: parentCategory.url_key,
      position: parentCategory.position,
      path: path.join('/'),
      children: childrenData,
    };
  }

  async fetchParentCategoryDetails(id: number) {
    try {
      const existingCategory = await this.categoryRepository.findOneBy({
        id,
      });

      if (!existingCategory)
        throw new BadRequestException('This category doesnot exist');

      const parentCategory = await this.categoryRepository.findOneBy({
        id: existingCategory.parent_id,
      });

      if (!parentCategory)
        throw new BadRequestException('This category doesnot exist');

      return {
        serched_category: id,
        parent_category: parentCategory,
      };
    } catch (error) {
      this.logger.error('Failed to fetch parent category', error);

      throw new InternalServerErrorException('Failed to fetch parent category');
    }
  }

  category_products_data_mapper(productsData: any[]) {
    let data = productsData.map((item) => {
      let visibilityValue =
        item.product?.catalogProductFlatRelations?.visibility;

      let visibilityStatus;
      switch (visibilityValue) {
        case 1:
          visibilityStatus = 'Not Visible Individually';
          break;
        case 2:
          visibilityStatus = 'Catalog';
          break;
        case 3:
          visibilityStatus = 'Search';
          break;
        case 4:
          visibilityStatus = 'Catalog, Search';
          break;
        default:
          visibilityStatus = null;
          break;
      }

      let thumbnailValue;

      if (item.product?.catalogProductFlatRelations?.thumbnail) {
        let thumbnail = item.product.catalogProductFlatRelations?.thumbnail;
        if (thumbnail.startsWith('s3/')) {
          thumbnail = thumbnail.replace(/^s3\//, '');
          thumbnail = `${env.aws.bucketBaseUrl}${thumbnail}`;
        } else if (!thumbnail.startsWith('http')) {
          thumbnail = `${env.magentoBaseImagesUrl}/${thumbnail}`;
        }

        thumbnailValue = thumbnail;
      }

      return {
        id: item.product?.id || null,
        sku: item.product?.sku || null,
        position: item.position,
        status:
          item.product?.status !== undefined && item.product.status !== null
            ? item.product.status
            : null,
        type_id: item.product?.type_id || null,
        stock_status:
          item.product?.inventoryAttributesRelations?.is_in_stock === true
            ? true
            : false,
        international_status:
          item.product?.catalogProductFlatRelations?.international_active ||
          null,
        name: item.product?.catalogProductFlatRelations?.name || null,

        thumbnail: thumbnailValue || null,

        visibility: visibilityStatus,
        price: item.product?.catalogProductFlatRelations?.price || null,

        selling_price:
          item.product?.catalogProductFlatRelations?.special_price || null,
      };
    });
    return data;
  }

  // UNCOMMENT THIS LATER IF YOU EVER WANT TO SYNC CATEGORIES WITH ELASTICSEARCH
  // async syncCategorywithElasticSearch(category: any) {
  //   try {
  //     let data = await this.getCategoryById([category]);
  //     // console.log(data);
  //     let categoryTableData = data[0];

  //     let productCategoryRows =
  //       await this.productCategoryRelationRepository.find({
  //         relations: ['product'],
  //         where: { category: { id: categoryTableData.id } },
  //       });
  //     // console.log(productCategoryRows);

  //     const modifiedData = {
  //       entity_id: categoryTableData.id,
  //       name: categoryTableData?.name,
  //       meta_title: categoryTableData?.meta_title,
  //       meta_keyword: categoryTableData?.meta_keyword,
  //       meta_description: categoryTableData?.meta_description,
  //       dynamic_banner: categoryTableData?.banner_web,
  //       web_link: categoryTableData?.banner_app,
  //       description: categoryTableData?.description,
  //       url_key: categoryTableData?.url_key,
  //       image: categoryTableData?.image,
  //       path: categoryTableData?.path,
  //       parent_id: categoryTableData?.parent_id,
  //     };

  //     // console.log(JSON.stringify(modifiedData), 'MF');

  //     const response =
  //       await this.externalApiHelper.sendCategoryInElastisticSearchEventBus(
  //         modifiedData,
  //       );

  //     // console.log('Response:', response);

  //     return {
  //       statusCode: response.status,
  //       body: JSON.stringify(response.data),
  //     };
  //   } catch (err) {
  //     this.logger.error('Failed to sync data with elastic search', err);
  //     throw new InternalServerErrorException(
  //       'Failed to sync data with elastic search',
  //     );
  //   }
  // }

  // async syncCategorywithMongo(
  //   categoryId: number,
  //   categoryData?: CatalogCategory,
  // ) {
  //   try {
  //     let categoryTableData;

  //     if (!categoryData) {
  //       let data = await this.getCategoryById([categoryId]);
  //       categoryTableData = data[0];
  //     } else {
  //       categoryTableData = categoryData;
  //     }

  //     let productCategoryRows =
  //       await this.productCategoryRelationRepository.find({
  //         relations: ['product'],
  //         where: { category: { id: categoryTableData.id } },
  //       });
  //     // console.log(productCategoryRows);

  //     const categoryProductsObject = productCategoryRows.reduce(
  //       (acc, e) => {
  //         const product_id = e.product?.id.toString();
  //         const position = e.position?.toString() || null; // Use `null` or any default value you prefer
  //         if (product_id) {
  //           acc[product_id] = position;
  //         }
  //         return acc;
  //       },
  //       {} as { [key: string]: string | null },
  //     );

  //     const modifiedData = {
  //       entity_id: Number(categoryTableData.id),
  //       is_active: categoryTableData.status === true ? '1' : '0',
  //       name: categoryTableData?.name,
  //       meta_title: categoryTableData?.meta_title,
  //       meta_keyword: categoryTableData?.meta_keyword,
  //       meta_description: categoryTableData?.meta_description,
  //       dynamic_banner: categoryTableData?.banner_web,
  //       web_link: categoryTableData?.banner_app,
  //       description: categoryTableData?.description,
  //       url_key: categoryTableData?.url_key,
  //       url_path: categoryTableData?.url_key,
  //       image: categoryTableData?.image,
  //       path: categoryTableData?.path,
  //       parent_id: categoryTableData?.parent_id,
  //       include_in_menu:
  //         categoryTableData?.include_in_menu == Boolean(1) ? '1' : '0',
  //       position:
  //         categoryTableData?.position == null
  //           ? ''
  //           : String(categoryTableData.position),
  //       category_products: JSON.stringify(categoryProductsObject),
  //     };

  //     const response =
  //       await this.externalApiHelper.sendCategoryInMongoDbEventBus(
  //         modifiedData,
  //       );

  //     console.log('Response:', response);

  //     return {
  //       statusCode: response.status,
  //       body: JSON.stringify(response.data),
  //     };
  //   } catch (err) {
  //     this.logger.error('Failed to sync data with mongodb', err);
  //     throw new InternalServerErrorException(
  //       'Failed to sync data with mongodb',
  //     );
  //   }
  // }

  async buildCategoryErpNotificationDataV2(categoryId: number[]) {
    try {
      //fetch catgeory and product information for sync
      const [categories, productCategoryRows] = await Promise.all([
        this.getCategoryById(categoryId),
        this.productCategoryRelationRepository.find({
          relations: ['product', 'category'],
          where: { category: { id: In(categoryId) } },
          select: {
            category: {
              id: true,
            },
            product: {
              id: true,
            },
          },
        }),
      ]);

      //build categoryProductsMap
      const categoryProductsMap = productCategoryRows.reduce(
        (acc, e) => {
          const categoryId = e.category.id.toString();
          const product_id = e.product?.id.toString();
          const position = e.position?.toString() || null;

          if (!acc[categoryId]) {
            acc[categoryId] = {};
          }

          if (product_id) {
            acc[categoryId][product_id] = position;
          }

          return acc;
        },
        {} as { [key: string]: { [key: string]: string | null } },
      );

      //category sync mapper
      const notifyData = categories.map((categoryTableData) => {
        const categoryProductsObject = categoryProductsMap[categoryTableData.id]
          ? categoryProductsMap[categoryTableData.id]
          : {};

        return {
          entity_id: Number(categoryTableData.id),
          is_active: categoryTableData.status === true ? '1' : '0',
          name: categoryTableData?.name,
          meta_title: categoryTableData?.meta_title,
          meta_keyword: categoryTableData?.meta_keyword,
          meta_description: categoryTableData?.meta_description,
          dynamic_banner: categoryTableData?.banner_web,
          web_link: categoryTableData?.banner_app,
          description: categoryTableData?.description,
          url_key: categoryTableData?.url_key,
          url_path: categoryTableData?.url_key,
          image: categoryTableData?.image,
          path: categoryTableData?.path,
          parent_id: categoryTableData?.parent_id,
          include_in_menu:
            categoryTableData?.include_in_menu == Boolean(1) ? '1' : '0',
          position:
            categoryTableData?.position == null
              ? ''
              : String(categoryTableData.position),
          category_products: JSON.stringify(categoryProductsObject),
        };
      });

      return notifyData;
    } catch (err) {
      this.logger.log(`Failed IDs ${JSON.stringify(categoryId)}`);
      this.logger.error(
        'Failed build Mongo sync notification data',
        err?.message,
      );
    }
  }

  // async updateProductsPositionInCategory(data: any) {
  //   try {
  //     let { category_id, product_positions_data } = data;
  //     if (!category_id || product_positions_data.length == 0) {
  //       throw new BadRequestException(
  //         'Please add both category id and product positions data in the request',
  //       );
  //     }

  //     const existingCategory = await this.categoryRepository.findOne({
  //       where: { id: category_id },
  //     });

  //     if (!existingCategory) {
  //       throw new BadRequestException('Invalid category');
  //     }

  //     const existingRelations =
  //       await this.productCategoryRelationRepository.find({
  //         where: {
  //           category: { id: category_id },
  //           product: product_positions_data.map((e) => ({ id: e?.id })),
  //         },
  //         relations: ['product', 'category'],
  //       });

  //     existingRelations.forEach((rel) => {
  //       const newData = product_positions_data.find(
  //         (data) => data?.id === rel.product.id,
  //       );
  //       if (newData) {
  //         rel.position = newData.position;
  //       }
  //     });

  //     await this.productCategoryRelationRepository.save(existingRelations);
  //     // await this.syncCategorywithElasticSearch(category_id);

  //     await this.syncCategorywithMongo(category_id);

  //     return { response: 'Positions updated successfully' };
  //   } catch (err) {
  //     this.logger.error('Failed to update products position in category', err);
  //     throw new InternalServerErrorException(
  //       'Failed to update products position in category',
  //     );
  //   }
  // }

  async updateProductsPositionInCategoryV2(data: any, headers: any) {
    const { category_id, product_positions_data } = data;

    if (!category_id || product_positions_data.length === 0) {
      throw new BadRequestException(
        'Please add both category id and product positions data in the request',
      );
    }

    const existingCategory = await this.categoryRepository.findOne({
      where: { id: category_id },
    });

    if (!existingCategory) {
      throw new BadRequestException('Category not found');
    }

    const existingRelations = await this.productCategoryRelationRepository.find(
      {
        where: {
          category: { id: category_id },
          product: product_positions_data.map((e) => ({ id: e?.id })),
        },
        relations: ['product', 'category'],
      },
    );

    if (!existingRelations.length) {
      return {
        response: 'No products found for input products id in category',
      };
    }

    const positionChanges = [];

    existingRelations.forEach((rel) => {
      const newData = product_positions_data.find(
        (data) => data?.id === rel.product.id,
      );
      if (newData && newData.position !== rel.position) {
        positionChanges.push({
          product_id: rel.product.id,
          previous_position: rel.position,
          new_position: newData.position,
        });
        rel.position = newData.position;
      }
    });

    if (positionChanges.length > 0) {
      // Wrap updates in a transaction

      const connection = this.entityManager.connection;
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Save updated positions
        await queryRunner.manager.save(
          ProductCategoryRelation,
          existingRelations,
        );

        // Log activity
        await this.eventsLogService.saveActivityAndEvent({
          headers,
          entityId: category_id,
          entityType: EntityType.CATEGORY,
          activityEntityType: EntityTypeEnum.CATEGORY,
          activityType: 'category-products-update',
          updatedValue: JSON.stringify(
            positionChanges.map((change) => ({
              product_id: change.product_id,
              new_position: change.new_position,
            })),
          ),
          previousValue: JSON.stringify(
            positionChanges.map((change) => ({
              product_id: change.product_id,
              previous_position: change.previous_position,
            })),
          ),
          queryRunner,
        });
        await queryRunner.commitTransaction();

        return { response: 'Positions updated successfully' };

        // Commit transaction
      } catch (err) {
        await queryRunner.rollbackTransaction();
        this.logger.error(
          'Failed to update products position in category',
          err,
        );
        throw new InternalServerErrorException(
          'Failed to update products position in category',
        );
      } finally {
        await queryRunner.release();
      }
    }

    // // Synchronize with Mongo (outside transaction)
    // await this.syncCategorywithMongo(category_id);
  }

  async removeProductsFromCategoryV2(
    categoryId: number,
    productIds: number[],
    headers: Record<string, string | string[]>,
  ) {
    // Step 1: Begin transaction for only critical operations
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const category = await queryRunner.manager.findOne(CatalogCategory, {
        where: { id: categoryId },
      });

      if (!category)
        throw new HttpException(
          'Category does not exist',
          HttpStatus.BAD_REQUEST,
        );

      // Step 1: Fetch all existing product relations outside of the transaction
      const existingRelations = await queryRunner.manager.find(
        ProductCategoryRelation,
        {
          where: { category: { id: categoryId } },
          relations: ['product'],
        },
      );

      const existingProductIds = existingRelations.map(
        (relation) => relation.product.id,
      );

      // Step 2: Identify relations to delete
      const relationsToDelete = existingRelations.filter((relation) =>
        productIds.includes(relation.product.id),
      );

      const productsToRemove = relationsToDelete.map(
        (relation) => relation.product.id,
      );

      // Step 3: Identify products that are not found
      const notFoundProducts = productIds.filter(
        (productId) => !productsToRemove.includes(productId),
      );

      // Early exit if no relations to delete
      if (!productsToRemove.length) {
        return { removedProducts: [], notFoundProducts };
      }

      // Remove the relations
      await queryRunner.manager.remove(
        ProductCategoryRelation,
        relationsToDelete,
      );

      // Log the activity
      await this.eventsLogService.saveActivityAndEvent({
        headers,
        entityId: categoryId,
        entityType: EntityType.CATEGORY,
        activityEntityType: EntityTypeEnum.CATEGORY,
        activityType: 'category-products-update',
        queryRunner: queryRunner,
        updatedValue: JSON.stringify(
          existingProductIds.filter(
            (productId) => !productsToRemove.includes(productId),
          ), // Products remaining after removal
        ),
        previousValue: JSON.stringify(existingProductIds), // All existing products before update
      });

      // Commit the transaction
      await queryRunner.commitTransaction();

      return { removedProducts: productsToRemove, notFoundProducts };
    } catch (error) {
      // Rollback the transaction on error
      await queryRunner.rollbackTransaction();
      this.logger.error('Failed to remove products from category', error);
      throw new InternalServerErrorException(
        'Failed to remove products from category',
      );
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  async updateOrMoveCategory(
    id: number,
    newParentId: number | null,
    newPosition: number,
    headers: Record<string, string | string[]>,
  ): Promise<{
    data: CatalogCategory;
    categoryChangesArray: CatalogCategory[];
  }> {
    return this.entityManager.transaction(
      async (transactionalEntityManager) => {
        try {
          const category = await transactionalEntityManager.findOne(
            CatalogCategory,
            {
              where: { id },
            },
          );

          if (!category) {
            throw new NotFoundException('Category not found');
          }

          if (!newParentId && !newPosition) {
            throw new BadRequestException(
              'Please provide new postion or new parent in order to update.',
            );
          }

          const idPositionMapper: {
            [key: number]: { position: number; parent_id: number };
          } = {
            [category.id]: {
              position: category.position,
              parent_id: category.parent_id,
            },
          };

          // case:1 If moving to a new parent
          if (newParentId && newParentId !== category.parent_id) {
            // Find all siblings in the old parent and update their positions
            const oldParentSiblings = await transactionalEntityManager.find(
              CatalogCategory,
              {
                where: { parent_id: category.parent_id },
                order: { position: 'ASC' },
              },
            );

            const promisesOldParent: Promise<CatalogCategory>[] = [];

            for (const sibling of oldParentSiblings) {
              if (
                sibling.id !== category.id &&
                sibling.position > category.position
              ) {
                idPositionMapper[sibling.id] = {
                  position: sibling.position,
                  parent_id: sibling.parent_id,
                };
                sibling.position--;
                promisesOldParent.push(
                  transactionalEntityManager.save(CatalogCategory, sibling),
                );
              }
            }
            const promisesNewParent: Promise<CatalogCategory>[] = [];

            if (newPosition) {
              // Find all siblings in the new parent and update their positions
              const newParentSiblings = await transactionalEntityManager.find(
                CatalogCategory,
                {
                  where: { parent_id: newParentId },
                  order: { position: 'ASC' },
                },
              );

              const position = newParentSiblings.some(
                (data) => data.position === newPosition,
              );

              if (!position) {
                newPosition = 1; //first element
              }

              for (const sibling of newParentSiblings) {
                if (sibling.position >= newPosition) {
                  idPositionMapper[sibling.id] = {
                    position: sibling.position,
                    parent_id: sibling.parent_id,
                  };
                  sibling.position++;
                  promisesNewParent.push(
                    transactionalEntityManager.save(CatalogCategory, sibling),
                  );
                }
              }
              category.position = newPosition;
            } else {
              const count = await transactionalEntityManager.count(
                CatalogCategory,
                {
                  where: { parent_id: newParentId },
                },
              );
              category.position = count + 1;
            }

            // Update the category's parentId and position
            category.parent_id = newParentId;
            await transactionalEntityManager.save(CatalogCategory, category);
            let newAndOldCategoryPromises = await Promise.all([
              ...promisesOldParent,
              ...promisesNewParent,
            ]);

            // Log the activity
            let logActivityPromise = [];
            for (const data of [...newAndOldCategoryPromises, category]) {
              logActivityPromise.push(
                this.eventsLogService.saveActivityAndEvent({
                  headers,
                  entityId: data.id,
                  entityType: EntityType.CATEGORY,
                  activityEntityType: EntityTypeEnum.CATEGORY,
                  activityType: 'category-position-parent-change',
                  queryRunner: transactionalEntityManager.queryRunner,
                  updatedValue: JSON.stringify({
                    id: data.id,
                    position: data.position,
                    parent_id: data.parent_id,
                  }),
                  previousValue: JSON.stringify({
                    id: data.id,
                    position: idPositionMapper[data.id]?.position,
                    parent_id: idPositionMapper[data.id]?.parent_id,
                  }),
                }),
              );
            }

            await Promise.all(logActivityPromise);

            return {
              data: category,
              categoryChangesArray: [...newAndOldCategoryPromises, category],
            };
          }

          if (!newPosition)
            throw new BadRequestException('Please provide newPosition ');

          if (category.position === newPosition) {
            return { data: category, categoryChangesArray: [] };
          }

          // case:2 Only swap the position within the same parent
          const siblings = await transactionalEntityManager.find(
            CatalogCategory,
            {
              where: { parent_id: category.parent_id },
              order: { position: 'ASC' },
            },
          );

          const position = siblings.some(
            (data) => data.position === newPosition,
          );

          if (!position) throw new BadRequestException('Invalid new position');

          const swapCategoryPromises: Promise<CatalogCategory>[] = [];

          for (const sibling of siblings) {
            if (sibling.id !== category.id) {
              if (newPosition < category.position) {
                // Moving up
                if (
                  sibling.position >= newPosition &&
                  sibling.position < category.position
                ) {
                  sibling.position++;
                  idPositionMapper[sibling.id] = {
                    position: sibling.position,
                    parent_id: sibling.parent_id,
                  };
                  swapCategoryPromises.push(
                    transactionalEntityManager.save(CatalogCategory, sibling),
                  );
                }
              } else {
                // Moving down
                if (
                  sibling.position > category.position &&
                  sibling.position <= newPosition
                ) {
                  idPositionMapper[sibling.id] = {
                    position: sibling.position,
                    parent_id: sibling.parent_id,
                  };
                  sibling.position--;
                  swapCategoryPromises.push(
                    transactionalEntityManager.save(CatalogCategory, sibling),
                  );
                }
              }
            }
          }

          // Update the category's position
          category.position = newPosition;
          await transactionalEntityManager.save(CatalogCategory, category);
          const resolvedSwapCategories =
            await Promise.all(swapCategoryPromises);

          // Log the activity
          let logActivityPromise = [];
          for (const data of [...resolvedSwapCategories, category]) {
            logActivityPromise.push(
              this.eventsLogService.saveActivityAndEvent({
                headers,
                entityId: data.id,
                entityType: EntityType.CATEGORY,
                activityEntityType: EntityTypeEnum.CATEGORY,
                activityType: 'category-position-parent-change',
                queryRunner: transactionalEntityManager.queryRunner,
                updatedValue: JSON.stringify({
                  id: data.id,
                  position: data.position,
                  parent_id: data.parent_id,
                }),
                previousValue: JSON.stringify({
                  id: data.id,
                  position: idPositionMapper[data.id]?.position,
                  parent_id: idPositionMapper[data.id]?.parent_id,
                }),
              }),
            );
          }

          await Promise.all(logActivityPromise);

          return {
            data: category,
            categoryChangesArray: [...resolvedSwapCategories, category],
          };
        } catch (e) {
          if (e instanceof BadRequestException || NotFoundException) {
            throw e;
          } else {
            throw new InternalServerErrorException(e?.message || e);
          }
        }
      },
    );
  }

  // async sendCategoryPositionUpdatesToEventBus(
  //   syncCategories: CatalogCategory[],
  // ) {
  //   try {
  //     const syncPromises = syncCategories.map(async (swapCategory) => {
  //       return this.syncCategorywithMongo(swapCategory.id, swapCategory);
  //     });

  //     await Promise.all([...syncPromises]);
  //   } catch (e) {
  //     if (e instanceof BadRequestException || NotFoundException) {
  //       throw e;
  //     } else {
  //       throw new InternalServerErrorException(e?.message || e);
  //     }
  //   }
  // }

  async addPositon() {
    try {
      const allCategories = await this.categoryRepository.find();
      const topLevel = allCategories.filter(
        (category) => category.parent_id === 1,
      );
      const catgoryPromisses: Promise<CatalogCategory>[] = [];
      topLevel.map((category, index) => {
        category.position = index + 1;
        catgoryPromisses.push(this.categoryRepository.save(category));
        this.recursiveFunc(category, catgoryPromisses, allCategories);
      });
      await Promise.all(catgoryPromisses);
      return 'success';
    } catch (error) {
      console.log(error);
    }
  }

  recursiveFunc(
    Pcategory: CatalogCategory,
    promise: Promise<CatalogCategory>[],
    allCategories: CatalogCategory[],
  ) {
    const children = allCategories.filter(
      (category) => category.parent_id === Pcategory.id,
    );

    children.map((category, index) => {
      category.position = index + 1;
      promise.push(this.categoryRepository.save(category));
    });

    if (children.length === 0) {
      return;
    }

    children.map((child) => this.recursiveFunc(child, promise, allCategories));
  }

  async categoryProductUpdateTransactionBlock(
    category: CatalogCategory,
    relationsToAdd: ProductCategoryRelation[],
    headers: Record<string, string | string[]>,
    existingProdIds: number[],
    newProductIds: number[],
  ) {
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const data = await queryRunner.manager.save(
        ProductCategoryRelation,
        relationsToAdd,
      );
      // await this.productCategoryRelationRepository.save(relationsToAdd);
      await this.eventsLogService.saveActivityAndEvent({
        headers,
        entityId: category.id,
        entityType: EntityType.CATEGORY,
        activityEntityType: EntityTypeEnum.CATEGORY,
        activityType: 'category-products-update',
        queryRunner: queryRunner,
        updatedValue: JSON.stringify(newProductIds),
        previousValue: JSON.stringify(existingProdIds),
      });
      await queryRunner.commitTransaction();
      return data;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw new InternalServerErrorException(
        'Failed to update category prod',
        e?.message || e,
      );
    } finally {
      await queryRunner.release();
    }
  }

  async createCatgory(
    body: CreateCatalogCategoryDto,
    queryRunner: QueryRunner,
    headers: Record<string, string | string[]>,
  ) {
    const { category_products, ...categoryData } = body;

    if (!categoryData.name || categoryData.name.length === 0) {
      throw new BadRequestException(
        'Please add category name before creating a new category',
      );
    }

    if (!categoryData.parent_id) {
      categoryData['parent_id'] = 1;
    }
    // else {
    //   const parent = await this.categoryRepository.findOne({
    //     where: { id: body.parent_id },
    //   });

    //   if (!parent)
    //     throw new HttpException(
    //       'Parent category does not exist',
    //       HttpStatus.BAD_REQUEST,
    //     );
    // }

    const existingCategoryNameForSameParentCategory =
      await this.categoryRepository.findOne({
        where: { name: categoryData.name, parent_id: categoryData.parent_id },
      });

    if (existingCategoryNameForSameParentCategory) {
      throw new BadRequestException(
        'A category with the same name already exists for the same parent category',
      );
    }

    const uniqueCategoryName = await this.generateUniqueCategoryName(
      categoryData.name,
    );

    const existingCategoryUrlInUrlRewrites =
      await this.urlRewritesRepository.findOne({
        where: { request_path: `${uniqueCategoryName}.html` },
      });

    if (existingCategoryUrlInUrlRewrites) {
      throw new BadRequestException(
        'Category url key created is not unique, please change category name',
      );
    }

    let categoryAdd: Partial<CatalogCategory> = {
      ...categoryData,
      url_key: uniqueCategoryName,
      parent_id: categoryData.parent_id === null ? 1 : categoryData.parent_id,
      include_in_menu: categoryData?.include_in_menu == true ? true : false,
    };

    if (categoryData.parent_id) {
      //add position while creating
      const count = await this.categoryRepository.count({
        where: { parent_id: categoryData.parent_id },
      });
      categoryAdd.position = count + 1;
    }

    // categoryData.url_key = uniqueCategoryName;

    const createdCategory = this.categoryRepository.create(categoryAdd);
    const savedCategory = await queryRunner.manager.save(
      CatalogCategory,
      createdCategory,
    );

    let url_rewrites_body = {
      entity_id: savedCategory.id,
      entity_type: EntityTypeEnum.CATEGORY,
      request_path: `${uniqueCategoryName}.html`,
      target_path: String(savedCategory.id),
    };

    const { createdUrlRewrite } =
      await this.catalogProductService.modifyUrlRewritesV2(
        url_rewrites_body,
        queryRunner,
      );

    await Promise.all([
      this.eventsLogService.saveActivityAndEvent({
        headers,
        entityId: savedCategory.id,
        entityType: EntityType.CATEGORY,
        activityEntityType: EntityTypeEnum.CATEGORY,
        activityType: 'create-category',
        queryRunner: queryRunner,
        updatedValue: null,
        previousValue: null,
      }),
      this.eventsLogService.saveActivityAndEvent({
        headers,
        entityId: createdUrlRewrite.id,
        entityType: EntityType.URL_REWRITE,
        activityEntityType: EntityTypeEnum.URL_REWRITE,
        activityType: 'create-category-url-rewrite',
        queryRunner: queryRunner,
        updatedValue: null,
        previousValue: null,
      }),
    ]);

    return {
      inputData: categoryData,
      category: savedCategory,
    };
  }

  // Category Banner Methods

  /**
   * Create a new category banner with activity logging
   */
  async createCategoryBanner(
    createCategoryBannerDto: CreateCategoryBannerDto,
    headers: Record<string, string | string[]>,
  ): Promise<CategoryBanner> {
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(
        `Creating category banner for category ID: ${createCategoryBannerDto.category_id}`,
      );

      // Verify that the category exists
      const category = await queryRunner.manager.findOne(CatalogCategory, {
        where: { id: createCategoryBannerDto.category_id },
      });

      if (!category) {
        throw new NotFoundException(
          `Category with ID ${createCategoryBannerDto.category_id} not found`,
        );
      }

      // Check for duplicate title within the same category
      const existingBanner = await queryRunner.manager.findOne(CategoryBanner, {
        where: {
          category_id: createCategoryBannerDto.category_id,
          title: createCategoryBannerDto.title,
        },
      });

      if (existingBanner) {
        throw new BadRequestException(
          `A banner with title "${createCategoryBannerDto.title}" already exists for this category`,
        );
      }

      const categoryBanner = queryRunner.manager.create(
        CategoryBanner,
        createCategoryBannerDto,
      );
      const savedBanner = await queryRunner.manager.save(
        CategoryBanner,
        categoryBanner,
      );

      // Log activity
      await this.eventsLogService.saveActivityAndEvent({
        headers,
        entityId: savedBanner.id,
        entityType: EntityType.CATEGORY,
        activityEntityType: EntityTypeEnum.CATEGORY,
        activityType: 'create-category-banner',
        queryRunner: queryRunner,
        updatedValue: savedBanner,
        previousValue: null,
      });

      await queryRunner.commitTransaction();

      this.logger.log(
        `Successfully created category banner with ID: ${savedBanner.id}`,
      );

      return savedBanner;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error('Failed to create category banner', {
        error: error.message,
        stack: error.stack,
        categoryId: createCategoryBannerDto.category_id,
        title: createCategoryBannerDto.title,
      });

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create category banner');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get all category banners with optional filtering and pagination
   */
  async getCategoryBanners(
    queryDto: CategoryBannerQueryDto,
    pagination?: { page: number; limit?: number },
  ): Promise<{
    data: CategoryBanner[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      this.logger.log(
        `Fetching category banners with filters: ${JSON.stringify(queryDto)}, pagination: ${JSON.stringify(pagination)}`,
      );

      const limit = pagination?.limit || env.sqlQueryResultsize;
      const page = pagination?.page || 1;
      const skip = (page - 1) * limit;

      const queryBuilder = this.categoryBannerRepository
        .createQueryBuilder('banner')
        .leftJoinAndSelect('banner.category', 'category')
        .orderBy('banner.sort_order', 'ASC')
        .addOrderBy('banner.created_at', 'DESC');

      if (queryDto.category_id) {
        queryBuilder.andWhere('banner.category_id = :categoryId', {
          categoryId: queryDto.category_id,
        });
      }

      if (queryDto.landing_page_type) {
        queryBuilder.andWhere('banner.landing_page_type = :landingPageType', {
          landingPageType: queryDto.landing_page_type,
        });
      }

      const [data, total] = await queryBuilder
        .skip(skip)
        .take(limit)
        .getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      this.logger.log(`Successfully fetched ${data.length} category banners`);

      return {
        data,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error('Failed to get category banners', {
        error: error.message,
        stack: error.stack,
        filters: queryDto,
        pagination,
      });
      throw new InternalServerErrorException('Failed to get category banners');
    }
  }

  /**
   * Get a single category banner by ID
   */
  async getCategoryBannerById(id: number): Promise<CategoryBanner> {
    try {
      this.logger.log(`Fetching category banner with ID: ${id}`);

      if (!id || id <= 0) {
        throw new BadRequestException('Invalid banner ID provided');
      }

      const banner = await this.categoryBannerRepository.findOne({
        where: { id },
        relations: ['category'],
      });

      if (!banner) {
        throw new NotFoundException(`Category banner with ID ${id} not found`);
      }

      this.logger.log(`Successfully fetched category banner with ID: ${id}`);
      return banner;
    } catch (error) {
      this.logger.error(`Failed to get category banner with ID ${id}`, {
        error: error.message,
        stack: error.stack,
        bannerId: id,
      });

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get category banner');
    }
  }

  /**
   * Update a category banner with activity logging
   */
  async updateCategoryBanner(
    id: number,
    updateCategoryBannerDto: UpdateCategoryBannerDto,
    headers: Record<string, string | string[]>,
  ): Promise<CategoryBanner> {
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(
        `Updating category banner with ID: ${id}, data: ${JSON.stringify(updateCategoryBannerDto)}`,
      );

      if (!id || id <= 0) {
        throw new BadRequestException('Invalid banner ID provided');
      }

      // Get existing banner
      const existingBanner = await queryRunner.manager.findOne(CategoryBanner, {
        where: { id },
        relations: ['category'],
      });

      if (!existingBanner) {
        throw new NotFoundException(`Category banner with ID ${id} not found`);
      }

      // Store original values for activity logging
      const previousValue = { ...existingBanner };

      // If category_id is being updated, verify the new category exists
      if (updateCategoryBannerDto.category_id) {
        const category = await queryRunner.manager.findOne(CatalogCategory, {
          where: { id: updateCategoryBannerDto.category_id },
        });

        if (!category) {
          throw new NotFoundException(
            `Category with ID ${updateCategoryBannerDto.category_id} not found`,
          );
        }
      }

      // Check for duplicate title if title is being updated
      if (
        updateCategoryBannerDto.title &&
        updateCategoryBannerDto.title !== existingBanner.title
      ) {
        const categoryIdToCheck =
          updateCategoryBannerDto.category_id || existingBanner.category_id;

        const duplicateBanner = await queryRunner.manager.findOne(
          CategoryBanner,
          {
            where: {
              category_id: categoryIdToCheck,
              title: updateCategoryBannerDto.title,
              id: Not(id),
            },
          },
        );

        if (duplicateBanner) {
          throw new BadRequestException(
            `A banner with title "${updateCategoryBannerDto.title}" already exists for this category`,
          );
        }
      }

      // Update the banner
      Object.assign(existingBanner, updateCategoryBannerDto);
      const updatedBanner = await queryRunner.manager.save(
        CategoryBanner,
        existingBanner,
      );

      // Log activity
      await this.eventsLogService.saveActivityAndEvent({
        headers,
        entityId: updatedBanner.id,
        entityType: EntityType.CATEGORY,
        activityEntityType: EntityTypeEnum.CATEGORY,
        activityType: 'update-category-banner',
        queryRunner: queryRunner,
        updatedValue: updatedBanner,
        previousValue: previousValue,
      });

      await queryRunner.commitTransaction();

      this.logger.log(`Successfully updated category banner with ID: ${id}`);

      return updatedBanner;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to update category banner with ID ${id}`, {
        error: error.message,
        stack: error.stack,
        bannerId: id,
        updateData: updateCategoryBannerDto,
      });

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update category banner');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Delete a category banner with activity logging
   */
  async deleteCategoryBanner(
    id: number,
    headers: Record<string, string | string[]>,
  ): Promise<void> {
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Deleting category banner with ID: ${id}`);

      if (!id || id <= 0) {
        throw new BadRequestException('Invalid banner ID provided');
      }

      const banner = await queryRunner.manager.findOne(CategoryBanner, {
        where: { id },
        relations: ['category'],
      });

      if (!banner) {
        throw new NotFoundException(`Category banner with ID ${id} not found`);
      }

      // Store banner data for activity logging before deletion
      const bannerData = { ...banner };

      // Delete the banner
      await queryRunner.manager.remove(CategoryBanner, banner);

      // Log activity
      await this.eventsLogService.saveActivityAndEvent({
        headers,
        entityId: id,
        entityType: EntityType.CATEGORY,
        activityEntityType: EntityTypeEnum.CATEGORY,
        activityType: 'delete-category-banner',
        queryRunner: queryRunner,
        updatedValue: null,
        previousValue: bannerData,
      });

      await queryRunner.commitTransaction();

      this.logger.log(`Successfully deleted category banner with ID: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to delete category banner with ID ${id}`, {
        error: error.message,
        stack: error.stack,
        bannerId: id,
      });

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to delete category banner');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get category banners by category ID with pagination
   */
  async getCategoryBannersByCategoryId(
    categoryId: number,
    pagination?: { page: number; limit?: number },
  ): Promise<{
    data: CategoryBanner[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      this.logger.log(
        `Fetching banners for category ID: ${categoryId}, pagination: ${JSON.stringify(pagination)}`,
      );

      if (!categoryId || categoryId <= 0) {
        throw new BadRequestException('Invalid category ID provided');
      }

      // Verify category exists
      const category = await this.categoryRepository.findOne({
        where: { id: categoryId },
      });

      if (!category) {
        throw new NotFoundException(
          `Category with ID ${categoryId} not found`,
        );
      }

      const limit = pagination?.limit || env.sqlQueryResultsize;
      const page = pagination?.page || 1;
      const skip = (page - 1) * limit;

      const [data, total] = await this.categoryBannerRepository.findAndCount({
        where: { category_id: categoryId },
        order: { sort_order: 'ASC', created_at: 'DESC' },
        skip,
        take: limit,
      });

      const totalPages = Math.ceil(total / limit);

      this.logger.log(
        `Successfully fetched ${data.length} banners for category ID: ${categoryId}`,
      );

      return {
        data,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get category banners for category ID ${categoryId}`,
        {
          error: error.message,
          stack: error.stack,
          categoryId,
          pagination,
        },
      );

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to get category banners for category',
      );
    }
  }

  /**
   * Get activity logs for category banners
   */
  async getCategoryBannerActivityLogs(
    bannerId: number,
    pagination?: { page: number; limit?: number },
  ): Promise<{
    data: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      this.logger.log(
        `Fetching activity logs for banner ID: ${bannerId}, pagination: ${JSON.stringify(pagination)}`,
      );

      if (!bannerId || bannerId <= 0) {
        throw new BadRequestException('Invalid banner ID provided');
      }

      // Verify banner exists
      const banner = await this.categoryBannerRepository.findOne({
        where: { id: bannerId },
      });

      if (!banner) {
        throw new NotFoundException(
          `Category banner with ID ${bannerId} not found`,
        );
      }

      const limit = pagination?.limit || env.sqlQueryResultsize;
      const page = pagination?.page || 1;
      const skip = (page - 1) * limit;

      // Get activity logs related to this banner
      const [data, total] = await this.activityLogsRepository.findAndCount({
        relations: ['activity'],
        where: {
          entity_id: bannerId,
          activity: {
            entity: EntityTypeEnum.CATEGORY,
            activity_type: In([
              'create-category-banner',
              'update-category-banner',
              'delete-category-banner',
            ]),
          },
        },
        order: { id: 'DESC' },
        skip,
        take: limit,
      });

      const totalPages = Math.ceil(total / limit);

      this.logger.log(
        `Successfully fetched ${data.length} activity logs for banner ID: ${bannerId}`,
      );

      return {
        data,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get activity logs for banner ID ${bannerId}`,
        {
          error: error.message,
          stack: error.stack,
          bannerId,
          pagination,
        },
      );

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to get category banner activity logs',
      );
    }
  }
}
