import {
  Controller,
  Post,
  Body,
  Get,
  Patch,
  Delete,
  Param,
  BadRequestException,
  UseInterceptors,
  UploadedFiles,
  InternalServerErrorException,
  ParseIntPipe,
  UseGuards,
  Query,
  Put,
  Headers,
  UploadedFile,
} from '@nestjs/common';
import { CatalogCategoryService } from './catalog-category.service';
import {
  AddProductToCategoryDto,
  CreateCatalogCategoryDto,
  UpdateCatalogCategoryDto,
  removeProductsFromCategoryDto,
} from './dto/catalog-category.dto';
import {
  CreateCategoryBannerDto,
  UpdateCategoryBannerDto,
  CategoryBannerQueryDto,
  CategoryBannerPaginationDto,
} from './dto/category-banner.dto';
import { UpdateOrMoveCategoryDto } from './dto/category-move.dto';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ApiKeyGuard } from 'src/guards/api-key.guards';
import multer from 'multer';

// @UseGuards(ApiKeyGuard)
@Controller('/v1/catalog-admin/category')
export class CatalogCategoryController {
  constructor(
    private readonly catalogCategoryService: CatalogCategoryService,
  ) {}

  //CRUD FOR MAIN CATALOG CATEGORY
  @Get()
  async getAllCategory(
    @Query('page_no', ParseIntPipe) page_no: number,
    @Query('category_name') category_name: string,
  ) {
    return this.catalogCategoryService.getAllCatalogCategory(
      page_no,
      category_name,
    );
  }

  @Post('search-categories')
  async getCategoryById(
    @Body()
    body: {
      category_ids: number[];
      filters?: { name: string };
      pagination?: { page: number };
    },
  ) {
    if (body.category_ids && body.filters?.name) {
      throw new BadRequestException('Either send Category id or name only ');
    }
    return this.catalogCategoryService.getCategoryById(
      body.category_ids,
      body.filters,
      body.pagination,
    );
  }

  @Post('category-products/:category_id')
  async getProductsInCatgory(
    @Param('category_id') categoryId: number,
    @Body() body: { filters: any; pagination: any },
  ) {
    if (!categoryId) {
      throw new BadRequestException('Category ID is required');
    }
    return this.catalogCategoryService.getProductsInCategory(
      categoryId,
      body.filters,
      body.pagination,
    );
  }

  @Post('update/:categoryId')
  @UseInterceptors(FileInterceptor('files')) 
  async updateCategory(
    @Body() body: UpdateCatalogCategoryDto,
    @UploadedFile() file: Express.Multer.File,
    @Param('categoryId', ParseIntPipe) categoryId: number,
    @Headers() headers: Record<string, string | string[]>,
  ) {
    return this.catalogCategoryService.updateCategory(
      body,
      file,
      categoryId,
      headers,
    );
  }

  @Patch('add-products')
  async addProductsInCategory(
    @Body() body: AddProductToCategoryDto,
    @Headers() headers: Record<string, string | string[]>,
  ) {
    return this.catalogCategoryService.addProductsToCategory(
      body.categoryId,
      body.product_ids.map((product) => ({
        id: product.product_id,
        position: product.position,
      })),
      headers,
    );
  }

  @Patch('remove-products')
  async removeProductsFromCategory(
    @Body()
    body: removeProductsFromCategoryDto,
    @Headers() headers: Record<string, string | string[]>,
  ) {
    return this.catalogCategoryService.removeProductsFromCategoryV2(
      body.categoryId,
      body.product_ids,
      headers,
    );
  }

  // @Post('upload-file/:categoryId')
  // @UseInterceptors(FilesInterceptor('files'))
  // async uploadFiles(
  //   @UploadedFiles() files: Express.Multer.File[],
  //   @Param('categoryId', ParseIntPipe) categoryId: number,
  // ) {
  //   try {
  //     if (!files) {
  //       throw new BadRequestException('File is required');
  //     }

  //     return await this.catalogCategoryService.uploadCategoryImage(
  //       files,
  //       categoryId,
  //     );
  //   } catch (err) {
  //     throw new InternalServerErrorException('Failed to upload file');
  //   }
  // }

  @Delete('images/:categoryId')
  async deleteCategoryImage(
    // @Body() data: { key: string }
    @Param('categoryId', ParseIntPipe) categoryId: number,
  ) {
    return await this.catalogCategoryService.deleteCategoryImages(categoryId);
  }

  @Delete(':id')
  async deleteEvent(@Param('id') id: number) {
    return await this.catalogCategoryService.deleteCatalogCategory(id);
  }

  @Post()
  async createCategory(
    @Body()
    body: CreateCatalogCategoryDto,
    @Headers() headers: Record<string, string | string[]>,
  ) {
    return this.catalogCategoryService.createCatalogCategory(body, headers);
  }

  @Get('category-tree')
  async getAllCategoryTree() {
    return this.catalogCategoryService.getCategoryTree();
  }

  @Get('fetch-parent/:id')
  async fetchParentCategoryDetails(@Param('id') id: number) {
    return this.catalogCategoryService.fetchParentCategoryDetails(id);
  }

  @Patch('products-position')
  async updateProductsPositionInCategory(
    @Body()
    body: {
      category_id: number;
      product_positions_data: { id: number; position: number }[];
    },
    @Headers() headers: Record<string, string | string[]>,
  ) {
    return this.catalogCategoryService.updateProductsPositionInCategoryV2(
      body,
      headers,
    );
  }

  @Put('update-hierarchy')
  async updateOrMoveCategory(
    @Body() updateOrMoveCategoryDto: UpdateOrMoveCategoryDto,
    @Headers() headers: Record<string, string | string[]>,
  ) {
    const { category_id, new_parent_id, new_position } =
      updateOrMoveCategoryDto;
    let { data, categoryChangesArray } =
      await this.catalogCategoryService.updateOrMoveCategory(
        +category_id,
        +new_parent_id,
        +new_position,
        headers,
      );

    // await this.catalogCategoryService.sendCategoryPositionUpdatesToEventBus(
    //   categoryChangesArray,
    // );

    return data;
  }

  @Get('sync-position')
  async sycnPostions() {
    return this.catalogCategoryService.addPositon();
  }

  // Category Banner Endpoints

  @Post('banners')
  async createCategoryBanner(
    @Body() createCategoryBannerDto: CreateCategoryBannerDto,
    @Headers() headers: Record<string, string | string[]>,
  ) {
    try {
      const result = await this.catalogCategoryService.createCategoryBanner(
        createCategoryBannerDto,
        headers,
      );
      return {
        success: true,
        message: 'Category banner created successfully',
        data: result,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get('banners')
  async getCategoryBanners(
    @Query() queryDto: CategoryBannerQueryDto,
  ) {
    try {
      const pagination = {
        page: queryDto.page || 1,
        limit: queryDto.limit,
      };

      const result = await this.catalogCategoryService.getCategoryBanners(
        queryDto,
        pagination,
      );

      return {
        success: true,
        message: 'Category banners retrieved successfully',
        ...result,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get('banners/:id')
  async getCategoryBannerById(@Param('id', ParseIntPipe) id: number) {
    try {
      const result = await this.catalogCategoryService.getCategoryBannerById(id);
      return {
        success: true,
        message: 'Category banner retrieved successfully',
        data: result,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get(':categoryId/banners')
  async getCategoryBannersByCategoryId(
    @Param('categoryId', ParseIntPipe) categoryId: number,
    @Query() paginationDto: CategoryBannerPaginationDto,
  ) {
    try {
      const pagination = {
        page: paginationDto.page || 1,
        limit: paginationDto.limit,
      };

      const result = await this.catalogCategoryService.getCategoryBannersByCategoryId(
        categoryId,
        pagination,
      );

      return {
        success: true,
        message: 'Category banners retrieved successfully',
        ...result,
      };
    } catch (error) {
      throw error;
    }
  }

  @Patch('banners/:id')
  async updateCategoryBanner(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCategoryBannerDto: UpdateCategoryBannerDto,
    @Headers() headers: Record<string, string | string[]>,
  ) {
    try {
      const result = await this.catalogCategoryService.updateCategoryBanner(
        id,
        updateCategoryBannerDto,
        headers,
      );
      return {
        success: true,
        message: 'Category banner updated successfully',
        data: result,
      };
    } catch (error) {
      throw error;
    }
  }

  @Delete('banners/:id')
  async deleteCategoryBanner(
    @Param('id', ParseIntPipe) id: number,
    @Headers() headers: Record<string, string | string[]>,
  ) {
    try {
      await this.catalogCategoryService.deleteCategoryBanner(id, headers);
      return {
        success: true,
        message: 'Category banner deleted successfully',
      };
    } catch (error) {
      throw error;
    }
  }

  @Get('banners/:id/activity-logs')
  async getCategoryBannerActivityLogs(
    @Param('id', ParseIntPipe) id: number,
    @Query() paginationDto: CategoryBannerPaginationDto,
  ) {
    try {
      const pagination = {
        page: paginationDto.page || 1,
        limit: paginationDto.limit,
      };

      const result = await this.catalogCategoryService.getCategoryBannerActivityLogs(
        id,
        pagination,
      );

      return {
        success: true,
        message: 'Activity logs retrieved successfully',
        ...result,
      };
    } catch (error) {
      throw error;
    }
  }
}
