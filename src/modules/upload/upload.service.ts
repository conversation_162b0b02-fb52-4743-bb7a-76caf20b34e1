import { Injectable, BadRequestException } from '@nestjs/common';
import { S3Service } from 'src/utils/s3service';
import { UploadImageDto, UploadImageResponseDto } from './dto/upload-image.dto';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import { Logger } from '@nestjs/common';

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);
  
  constructor(private readonly s3Service: S3Service) {}

  async uploadImage(
    uploadDto: UploadImageDto,
    file: Express.Multer.File,
  ): Promise<UploadImageResponseDto> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Validate file type (only images)
    const allowedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Invalid file type. Only image files are allowed.',
      );
    }

    try {
      // Generate unique filename to avoid conflicts
      const fileExtension = path.extname(file.originalname);
      const uniqueFileName = `${uuidv4()}${fileExtension}`;
      
      // Construct the full path for S3
      const s3Path = uploadDto.path ? `${uploadDto.path}/${uniqueFileName}` : uniqueFileName;
      
      // Create a modified file object with the unique name
      const modifiedFile: Express.Multer.File = {
        ...file,
        originalname: uniqueFileName,
      };

      // Upload to S3 using the existing service
      const uploadedUrls = await this.s3Service.uploadFilesToS3(
        [modifiedFile],
        uploadDto.path,
      );

      if (!uploadedUrls || uploadedUrls.length === 0) {
        throw new BadRequestException('Failed to upload file to S3');
      }

      return new UploadImageResponseDto(uploadedUrls[0]);
    } catch (error) {
      throw new BadRequestException(
        `Failed to upload image: ${error.message}`,
      );
    }
  }
  
  /**
   * Upload any file (images, videos, etc.) to S3
   * @param file The file to upload
   * @param path The path where the file should be stored in S3
   * @returns Object containing the file URL
   */
  async uploadFile(
    file: Express.Multer.File,
    path: string,
  ): Promise<{ file_url: string | null }> {
    try {
      const maxFileSizeMB = 15; // Set max file size limit to 15MB
      if (file.size > maxFileSizeMB * 1024 * 1024) {
        throw new BadRequestException(
          `File size exceeds the limit of ${maxFileSizeMB}MB`,
        );
      }
 
      const fileExtension = file.originalname.split('.').pop();
      const fileName = `${uuidv4()}.${fileExtension}`;
      const uploadPath = `${path}/${fileName}`;
      
      // Create params for S3 upload
      const params = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: uploadPath,
        Body: file.buffer,
        ContentType: file.mimetype,
      };
      
      // Upload to S3
      const result = await this.s3Service.s3.upload(params).promise();
      
      return { file_url: result?.Location ?? null };
    } catch (error) {
      this.logger.error(`Error uploading file: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to upload file: ${error.message}`);
    }
  }
}
