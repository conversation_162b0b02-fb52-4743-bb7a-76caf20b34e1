import {
  Controller,
  Post,
  Body,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import { UploadImageDto, UploadImageResponseDto } from './dto/upload-image.dto';
import * as multer from 'multer';

@Controller()
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('catalog-product/upload/image')
  @UseInterceptors(FileInterceptor('files[0]'))
  async uploadImage(
    @Body() uploadDto: UploadImageDto,
    @UploadedFile() file: Express.Multer.File,
  ): Promise<UploadImageResponseDto> {
    if (!file) {
      throw new BadRequestException('No file provided in files[0]');
    }

    return this.uploadService.uploadImage(uploadDto, file);
  }

  @Post('catalog/v1/file/upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: multer.memoryStorage(),
      fileFilter: (req, file, callback) => {
        if (
          file.mimetype.match(
            /\/(jpg|jpeg|png|gif|webp|svg\+xml|mp4|avi|mov|wmv|flv|mkv|webm|mpg|mpeg|3gp|pdf|csv|vnd\.ms-excel|vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet)$/,
          ) || file.mimetype === 'application/pdf' || file.mimetype === 'text/csv'
        ) {
          callback(null, true);
        } else {
          callback(new BadRequestException('Invalid file type'), false);
        }
      },
    }),
  )
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body('path') path: string,
  ) {
    if (!file) {
      throw new BadRequestException('File is required');
    }
    return this.uploadService.uploadFile(file, path || 'catalog');
  }
}
