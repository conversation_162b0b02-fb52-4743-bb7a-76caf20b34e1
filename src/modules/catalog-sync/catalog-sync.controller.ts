import {
  Body,
  Controller,
  Get,
  Put,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { CatalogSyncService } from './catalog-sync.service';
import { ApiKeyGuard } from 'src/guards/api-key.guards';
import { UpdateOutboxDto } from './dtos/event-ack-dto';

@UseGuards(ApiKeyGuard)
@Controller('/v1/catalog-admin/sync')
export class CatalogSyncController {
  constructor(private readonly catalogSyncService: CatalogSyncService) {}

  @Put('acknowledge')
  async updateOutboxTable(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    updateOutboxDto: UpdateOutboxDto,
  ) {
    const { event_ids, entityType } = updateOutboxDto;
    return {
      isError: false,
      message: 'Outbox Table updated successfully',
      ids: event_ids,
      data: "",
    };
    return this.catalogSyncService.updateOutboxTable(event_ids, entityType);
  }

  @Get('/products')
  async productSync() {
    const tr = await this.catalogSyncService.productSync();
    return tr;
  }
  @Get('/category')
  async catgoeySync() {
    const tr = await this.catalogSyncService.categorySync();
    return tr;
  }

  @Get('/url-rewrite')
  async urlRewriteSync() {
    this.catalogSyncService.urlRewriteSync();
  }
}
