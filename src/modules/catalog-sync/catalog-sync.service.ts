import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  EntityType,
  EventsOutbox,
} from 'src/database/entities/outbox/event-outbox.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import env from 'src/config/env';
import { LoggerService } from 'src/utils/logger-service';
import { CatalogCategoryService } from '../main-catalog/catalog-category/catalog-category.service';
import { ExternalApiHelper } from 'src/utils/external-api-helper';
import { CatalogProductService } from '../main-catalog/catalog-product/catalog-product.service';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class CatalogSyncService {
  constructor(
    @InjectRepository(EventsOutbox)
    private readonly eventOurBoxRepo: Repository<EventsOutbox>,
    private readonly logger: LoggerService,
    private readonly catalogCategoryService: CatalogCategoryService,
    private readonly catalogProductService: CatalogProductService,

    private readonly externalApiHelper: ExternalApiHelper,
  ) {}

  async updateOutboxTable(event_ids: number[], entityType: EntityType) {
    try {
      const ack = await this.eventOurBoxRepo
        .createQueryBuilder()
        .update()
        .set({ received_by_es: true })
        .where('id IN (:...ids)', { ids: event_ids })
        .andWhere('entity_type = :entityType', {
          entityType: entityType,
        })
        .execute();

      return {
        isError: false,
        message: 'Outbox Table updated successfully',
        ids: event_ids,
        data: ack,
      };
    } catch (e) {
      this.logger.error('Failed to update outbox table ', e);
      return {
        isError: true,
        message: 'Failed to update outbox table ',
        error: e?.message || e,
      };
    }
  }

  private async getPendingEvents(entityType: EntityType) {

    const data = this.eventOurBoxRepo
      .createQueryBuilder('events_outbox')
      .select('GROUP_CONCAT(events_outbox.id)', 'event_id')
      .addSelect('events_outbox.entity_id')
      .where(
        '(events_outbox.is_published = :isPublished OR events_outbox.received_by_es = :receivedByEs)',
        { isPublished: 0, receivedByEs: 0 },
      )
      .andWhere('events_outbox.process_count < :processCount', {
        processCount: env.outbox_api_constants.max_process_count,
      })
      .andWhere('events_outbox.entity_type = :entityType', {
        entityType: entityType,
      })
      // .andWhere('events_outbox.updated_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)')
      .groupBy('events_outbox.entity_id')
      .orderBy('events_outbox.id', 'DESC')
      .limit(env.outbox_api_constants.products_count)
      .getRawMany();

    return data;
  }

  private async processCategoryNotification(
    category: any,
    outboxIds: number[],
  ) {
    // Update outbox entries and send notification
    await Promise.all([
      this.updateOutboxEntries(outboxIds),
      this.externalApiHelper.sendCategoryInMongoDbEventBus(category, outboxIds),
    ]);
  }

  private async processProductNotification(
    product: Record<string, any>,
    outboxIds: number[],
  ) {
    try {
      // Log the product and outbox IDs being processed
      this.logger.log(`Processing notification for product ID ${product.id}, SKU ${product.sku}, with outbox IDs: ${outboxIds.join(', ')}`);
      
      // Check if we have valid outbox IDs
      if (!outboxIds.length) {
        this.logger.warn(`No outbox IDs provided for product ${product.id}, notification might not be complete`);
      }
      
      // Run both operations but handle potential errors separately
      const [outboxUpdateResult, syncResult] = await Promise.allSettled([
        this.updateOutboxEntries(outboxIds),
        this.catalogProductService.syncProductwithMongo(product, outboxIds),
      ]);
      
      // Handle outbox update result
      if (outboxUpdateResult.status === 'rejected') {
        this.logger.error(
          `Failed to update outbox entries for product ${product.id}: ${outboxUpdateResult.reason.message}`,
          outboxUpdateResult.reason
        );
      } else {
        this.logger.log(`Successfully updated outbox entries for product ${product.id}`);
      }
      
      // Handle sync result
      if (syncResult.status === 'rejected') {
        this.logger.error(
          `Failed to sync product ${product.id} with MongoDB: ${syncResult.reason.message}`,
          syncResult.reason
        );
        throw syncResult.reason;
      } else {
        this.logger.log(`Successfully synced product ${product.id} to MongoDB`);
      }
      
      return syncResult.status === 'fulfilled' ? syncResult.value : null;
    } catch (error) {
      this.logger.error(
        `Exception in processProductNotification for product ${product?.id || 'unknown'}: ${error.message}`,
        error
      );
      throw error;
    }
  }

  private async processUrlRewriteNotification(
    data: Record<string, any>,
    outboxIds: number[],
  ) {
    // Update outbox entries and send notification
    await Promise.all([
      this.updateOutboxEntries(outboxIds),
      this.externalApiHelper.notifyUrlRewriteData(data, outboxIds),
    ]);
  }

  private mapOutboxEvents(
    results: { events_outbox_entity_id: number; event_id: string }[],
  ): Record<number, number[]> {
    return results.reduce(
      (map, event) => {
        map[event.events_outbox_entity_id] = event.event_id
          .split(',')
          .map(Number);
        return map;
      },
      {} as Record<number, number[]>,
    );
  }

  private async updateOutboxEntries(outboxIds: number[]) {
    await this.eventOurBoxRepo
      .createQueryBuilder()
      .update()
      .set({
        is_published: true,
        process_count: () => 'process_count + 1',
      })
      .where('id IN (:...ids)', { ids: outboxIds })
      .execute();
  }

  async categorySync() {
    try {
      console.log('+++Category_cron_staretd+++++');

      const results = await this.getPendingEvents(EntityType.CATEGORY);

      if (results?.length === 0) return;
      

      // Extract category IDs and outbox mapping
      const categoryIds = results.map((e) => e.events_outbox_entity_id);
      const outboxMap = this.mapOutboxEvents(results);

      // Fetch notification data for categories
      const notificationData =
        await this.catalogCategoryService.buildCategoryErpNotificationDataV2(
          categoryIds,
        );

      if (!notificationData?.length) return;

      console.log(
        '+++Category_sync_data+++++',
        JSON.stringify(notificationData),
      );

      // Process notifications and update outbox
      await Promise.all(
        notificationData.map(async (category) => {
          const outboxIds = outboxMap[+category.entity_id] || [];
          return this.processCategoryNotification(category, outboxIds);
        }),
      );

      console.log('+++Category_sync_finished+++++');

      return {
        message: 'Category sync completed',
        processed: results.length,
        results,
      };
    } catch (err) {
      console.log('Error_Category_sync_data:', err);
      this.logger.error('Failed to process category sync cron', err);
    }
  }

  async productSync() {
    let startTime = Date.now();
    let totalProductsProcessed = 0;
    let totalProductsAttempted = 0;
    let failedProducts = [];
    
    try {
      this.logger.log('Product sync cron job started');
      
      // Configuration for batch processing
      // Use existing properties or fallback to safe defaults
      const PRODUCT_BATCH_SIZE = 10; // Process 10 products at a time
      const FETCH_BATCH_SIZE = Math.min(50, env.outbox_api_constants.products_count || 50); // Fetch 50 products at a time but not more than configured limit
      const DELAY_BETWEEN_BATCHES = 100; // 100ms delay between batches
      const MAX_BATCH_RETRIES = 2; // Maximum number of retries per product
      
      // Get pending events
      const results = await this.getPendingEvents(EntityType.PRODUCT);
      if (!results?.length) {
        this.logger.log('No pending product events to process');
        return {
          message: 'No pending product events to process',
          processed: 0,
          elapsedMs: Date.now() - startTime,
        };
      }
      
      this.logger.log(`Found ${results.length} pending product events to process`);
      
      // Extract product IDs and map outbox events
      const productIds = results.map((e) => e.events_outbox_entity_id);
      const outboxMap = this.mapOutboxEvents(results);
      totalProductsAttempted = productIds.length;
      
      // Process products in larger fetch batches to reduce database load
      for (let fetchIndex = 0; fetchIndex < productIds.length; fetchIndex += FETCH_BATCH_SIZE) {
        const fetchBatchStart = Date.now();
        const fetchProductIds = productIds.slice(fetchIndex, fetchIndex + FETCH_BATCH_SIZE);
        
        this.logger.log(`Fetching product details for batch ${Math.floor(fetchIndex/FETCH_BATCH_SIZE) + 1} of ${Math.ceil(productIds.length/FETCH_BATCH_SIZE)} (${fetchProductIds.length} products)`);
        
        try {
          // Get product details for current fetch batch
          const productDetails = await this.catalogProductService.getProductsByIds(
            fetchProductIds,
            null,
            null,
            null,
          );
          
          if (!productDetails?.items?.length) {
            this.logger.warn(`No products found for batch with IDs: ${fetchProductIds.join(', ')}`);
            continue;
          }
          
          this.logger.log(`Retrieved ${productDetails.items.length} products, processing in smaller batches of ${PRODUCT_BATCH_SIZE}`);
          
          // Process the fetched products in smaller processing batches
          for (let i = 0; i < productDetails.items.length; i += PRODUCT_BATCH_SIZE) {
            const processingBatchStart = Date.now();
            const batchProducts = productDetails.items.slice(i, i + PRODUCT_BATCH_SIZE);
            
            // Array to collect batch processing results for reporting
            const batchResults = await Promise.allSettled(
              batchProducts.map(async (product) => {
                const outboxIds = outboxMap[+product.id] || [];
                let retryCount = 0;
                
                while (retryCount <= MAX_BATCH_RETRIES) {
                  try {
                    this.logger.log(`Processing product ${product.id} (${product.sku || 'unknown sku'})`);
                    await this.processProductNotification(product, outboxIds);
                    totalProductsProcessed++;
                    return { id: product.id, sku: product.sku, success: true };
                  } catch (error) {
                    if (retryCount < MAX_BATCH_RETRIES) {
                      this.logger.warn(`Retry ${retryCount+1}/${MAX_BATCH_RETRIES} for product ${product.id} (${product.sku || 'unknown sku'}): ${error.message}`);
                      retryCount++;
                      // Exponential backoff for retries
                      await new Promise(resolve => setTimeout(resolve, 100 * Math.pow(2, retryCount)));
                    } else {
                      failedProducts.push({ id: product.id, sku: product.sku || 'unknown', error: error.message || 'Unknown error' });
                      return { id: product.id, sku: product.sku, success: false, error: error.message };
                    }
                  }
                }
              })
            );
            
            // Report processing batch statistics
            const successCount = batchResults.filter(r => r.status === 'fulfilled' && r.value?.success).length;
            const failCount = batchResults.length - successCount;
            
            this.logger.log(`Processing batch completed: ${successCount}/${batchResults.length} successful, ${failCount} failed, took ${Date.now() - processingBatchStart}ms`);
            
            // Add a small delay between processing batches and hint for garbage collection
            if (i + PRODUCT_BATCH_SIZE < productDetails.items.length) {
              await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
              if (global.gc) {
                try {
                  global.gc();
                } catch (e) { /* ignore GC errors */ }
              }
            }
          }
          
          this.logger.log(`Fetch batch completed, took ${Date.now() - fetchBatchStart}ms`);
          
        } catch (fetchError) {
          this.logger.error(`Error fetching or processing batch ${Math.floor(fetchIndex/FETCH_BATCH_SIZE) + 1}: ${fetchError.message}`, fetchError);
          // Continue with next batch even if one batch fails
        }
        
        // Add a larger delay between fetch batches
        if (fetchIndex + FETCH_BATCH_SIZE < productIds.length) {
          await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES * 2));
          // Hint for garbage collection between major batches
          if (global.gc) {
            try {
              global.gc();
            } catch (e) { /* ignore GC errors */ }
          }
        }
      }
      
      const elapsedMs = Date.now() - startTime;
      this.logger.log(`Product sync completed: ${totalProductsProcessed}/${totalProductsAttempted} products processed successfully in ${elapsedMs}ms`);
      
      // Log failed products summary if any
      if (failedProducts.length > 0) {
        this.logger.warn(`${failedProducts.length} products failed to sync. First 5 failures: ${JSON.stringify(failedProducts.slice(0, 5))}`);
      }
      
      return {
        message: 'Product sync completed',
        processed: totalProductsProcessed,
        failed: failedProducts.length,
        total: totalProductsAttempted,
        elapsedMs: elapsedMs
      };
    } catch (err) {
      const elapsedMs = Date.now() - startTime;
      this.logger.error(
        `Failed to complete product sync after ${elapsedMs}ms: ${err.message}`,
        err,
      );
      
      return {
        message: 'Product sync failed',
        processed: totalProductsProcessed,
        failed: failedProducts.length,
        total: totalProductsAttempted,
        error: err.message || 'Unknown error',
        elapsedMs: elapsedMs
      };
    }
  }

  async urlRewriteSync() {
    try {
      console.log('+++urlRewriteSync_cron_started+++++');

      const results = await this.getPendingEvents(EntityType.URL_REWRITE);
      if (results?.length === 0) return;

      const urlRewriteIds = results.map((e) => e.events_outbox_entity_id);
      const outboxMap = this.mapOutboxEvents(results);
      const urlRewritesData =
        await this.catalogProductService.getUrlRewriteByIds(urlRewriteIds);

      console.log(
        '+++urlRewriteSync_data+++++',
        JSON.stringify(urlRewritesData),
      );

      if (!urlRewritesData?.length) return;

      await Promise.all(
        urlRewritesData.map(async (rewrite) => {
          const outboxIds = outboxMap[+rewrite.id] || [];
          return this.processUrlRewriteNotification(rewrite, outboxIds);
        }),
      );

      console.log('+++urlRewriteSync_finished+++++');

      return {
        message: 'Product sync completed',
        processed: results.length,
        results,
      };
    } catch (err) {
      console.log('+++urlRewriteSync_Error++++', err);

      this.logger.error(
        'Failed to push events in event bus for product sync',
        err,
      );
    }
  }
}
