import {
  Injectable,
  CanActivate,
  ExecutionContext,
} from '@nestjs/common';


@Injectable()
export class Validate<PERSON>eadersGuard implements CanActivate {
  //   constructor(private requiredHeaders: string[]) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const adminIdentifier = request.headers['admin-identifier'];

    // If admin-identifier is missing, empty, null, or undefined, set default value
    if (!adminIdentifier || adminIdentifier === 'null' || adminIdentifier === 'undefined' || adminIdentifier.trim() === '') {
      console.log('Admin identifier missing or empty, setting default:', {
        received: adminIdentifier,
        type: typeof adminIdentifier,
        url: request.url,
        method: request.method,
        defaultValue: 'admin_user'
      });

      // Set default value instead of throwing error
      request.headers['admin-identifier'] = 'admin_user';
    }

    return true;
  }
}
