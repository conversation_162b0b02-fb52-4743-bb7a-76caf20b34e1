import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ProductCategoryRelation } from './product-category-relation.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('catalog_category')
export class CatalogCategory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false, length: 50 })
  name: string;

  @Column({ default: false })
  status: boolean;

  @Column({ default: true })
  include_in_menu: boolean;

  @Column({ nullable: true })
  meta_title: string;

  @Column({ nullable: true, type: 'text' })
  meta_description: string;

  @Column({ nullable: true })
  banner_web: string;

  @Column({ nullable: true })
  banner_app: string;

  @Column({ nullable: true, type: 'text' })
  description: string;

  @Column({ nullable: false })
  url_key: string;

  @Column({ nullable: true, type: 'text' })
  meta_keyword: string;

  @Column({ nullable: true })
  image: string;

  @Column({ nullable: true })
  path: string;

  @Column({ default: 2 })
  parent_id: number;

  @Column({ nullable: true })
  web_link: string;

  @Column({ nullable: true })
  banner_title: string;

  @OneToMany(() => ProductCategoryRelation, (relation) => relation.category, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  categoryRelation: ProductCategoryRelation[];

  @OneToMany('CategoryBanner', 'category', {
    cascade: true,
    onDelete: 'CASCADE',
  })
  categoryBanners: any[];

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  @Column({ type: 'int', default: 0 })
  position: number;

  constructor(input?: Partial<CatalogCategory>) {
    if (input) Object.assign(this, input);
  }
}
