import {
  Column,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_inventory_attribute')
export class InventoryAttributes {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToOne(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.productCategoryRelations,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: false, default: false })
  is_in_stock: boolean;

  @Column({ nullable: false, default:0 })
  qty: number;

  @Column({ nullable: false, default: 1 })
  min_sale_qty: number;

  @Column({ nullable: false, default: 100 })
  max_sale_qty: number;

  @Column({ nullable: false, default: false })
  backorders: boolean;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<InventoryAttributes>) {
    if (input) Object.assign(this, input);
  }
}
