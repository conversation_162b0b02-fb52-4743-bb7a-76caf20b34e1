import { Injectable } from '@nestjs/common';
import {
  EventsOutbox,
  EntityType,
} from 'src/database/entities/outbox/event-outbox.entity';
import { Activity } from 'src/database/entities/product/activity.entity';
import { ActivityLogs } from 'src/database/entities/product/activity-logs.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { QueryRunner, Repository } from 'typeorm';
import {
  ActivityAndEvent,
  ActivityType,
} from '../modules/main-catalog/catalog-category/interfaces/activity.interface';

@Injectable()
export class EventsLogService {
  constructor(
    @InjectRepository(ActivityLogs)
    private readonly activityLogsRepo: Repository<ActivityLogs>,
  ) {}

  async saveActivityAndEvent<T>(
    params: ActivityAndEvent<T>,
  ): Promise<EventsOutbox> {
    try {
      const {
        activityType,
        updatedValue,
        previousValue,
        queryRunner,
        headers,
        entityId,
        entityType,
        activityEntityType,
      } = params;


      console.log("params",params);
      const { platform, user_agent } = headers || {};
      const user_meta_info = {
        ...(platform && { platform }),
        ...(user_agent && { user_agent }),
      };

      const activity = queryRunner.manager.create(Activity, {
        user: (headers['admin-identifier'] || 'admin_user') as string,
        entity: activityEntityType,
        activity_type: activityType,
        user_meta_info:
          Object.keys(user_meta_info).length === 0
            ? null
            : JSON.stringify(user_meta_info),
      });

      const savedActivity = await queryRunner.manager.save(Activity, activity);

      const savedlog = await this.createOrUpdateLog(
        queryRunner,
        savedActivity,
        entityId,
        updatedValue,
        previousValue,
        activityType,
        entityType,
      );

      return savedlog;
    } catch (error) {
      throw new Error(`Failed to save activity: ${error.message}`);
    }
  }

  async createOrUpdateLog<T>(
    queryRunner: QueryRunner,
    activity: Activity,
    entityId: number,
    newValues: T,
    oldValues: T,
    activityType: ActivityType,
    entityType: EntityType,
  ): Promise<EventsOutbox> {
    try {
      let oldValue = null,
        newValue = null;

      if (oldValues) {
        if (
          activityType === 'category-products-update' ||
          activityType === 'category-position-parent-change'
        ) {
          oldValue = JSON.stringify(oldValues);
          newValue = JSON.stringify(newValues);
        } else {
          const changedValues = this.mapChangedValues(oldValues, newValues);
          if (changedValues) {
            oldValue = JSON.stringify(changedValues.oldValue);
            newValue = JSON.stringify(changedValues.newValue);
          }
        }
      }

      const activityLog = queryRunner.manager.create(ActivityLogs, {
        entity_id: entityId,
        activity: activity,
        old_value: oldValue,
        new_value: newValue,
      });

      const logdata = await queryRunner.manager.save(ActivityLogs, activityLog);

      const data = await this.createEventTableEntry(
        queryRunner,
        logdata,
        entityId,
        entityType,
      );

      return data;
    } catch (error) {
      throw new Error(`Failed to save log: ${error.message}`);
    }
  }

  async createEventTableEntry(
    queryRunner: QueryRunner,
    activityLogs: ActivityLogs,
    entityId: number,
    entityType: EntityType,
  ) {
    try {
      const addEventsOutbox = queryRunner.manager.create(EventsOutbox, {
        entity_id: entityId,
        entity_type: entityType,
        activity_logs: activityLogs,
        is_published: false,
        received_by_es: false,
        process_count: 0,
      });
      const saveEventsOutbox = await queryRunner.manager.save(
        EventsOutbox,
        addEventsOutbox,
      );
      return saveEventsOutbox;
    } catch (error) {
      throw new Error(`Failed to update event box table: ${error.message}`);
    }
  }

  async findLogsByActivity(activityId: number): Promise<ActivityLogs[]> {
    const logs = await this.activityLogsRepo.find({
      where: { activity: { id: activityId } },
      relations: ['activity'],
    });
    return logs;
  }

  mapChangedValues<T>(
    oldValues: Partial<T> | null,
    newValues: Partial<T> | null,
  ): { oldValue: Partial<T>; newValue: Partial<T> } | null {
    try {
      if (!oldValues) {
        return null;
      }

      if (!newValues) {
        return null;
      }

      if (typeof oldValues !== 'object' || typeof newValues !== 'object') {
        return null;
      }

      const changedOldValues: Partial<T> = {};
      const changedNewValues: Partial<T> = {};

      Object.keys(newValues).forEach((key) => {
        if (oldValues[key] !== newValues[key]) {
          changedOldValues[key] = oldValues[key];
          changedNewValues[key] = newValues[key];
        }
      });

      if (Object.keys(changedOldValues).length > 0) {
        return { oldValue: changedOldValues, newValue: changedNewValues };
      }

      return null;
    } catch (error) {
      console.error('Error in mapChangedValues:', error);
      console.error('oldValues:', oldValues);
      console.error('newValues:', newValues);
      return null;
    }
  }
}
