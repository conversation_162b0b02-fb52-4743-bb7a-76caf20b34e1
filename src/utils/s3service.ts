import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import * as fs from 'fs';

@Injectable()
export class S3Service {
  public s3: AWS.S3;
  constructor() {
    this.s3 = new AWS.S3({
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
      },
    });
  }

  async uploadFilesToS3(
    files: Express.Multer.File[],
    folder: string = '',
  ): Promise<string[]> {
    try {
      const uploadPromises = [];
      const folderPath = folder?.endsWith('/') ? folder : `${folder}/`;

      for (const file of files) {
        const params = {
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: folder ? `${folderPath}${file.originalname}` : file.originalname,
          // Key: file.originalname,
          Body: file.buffer,
          ContentType: file.mimetype,
        };

        const uploadPromise = new Promise<string>((resolve, reject) => {
          this.s3.upload(params, (err, data) => {
            if (err) {
              console.log(err);
              reject(err);
            } else {
              resolve(data.Location);
            }
          });
        });

        uploadPromises.push(uploadPromise);
      }

      return Promise.all(uploadPromises);
    } catch (error) {
      throw new Error(`Failed to upload files to S3: ${error.message}`);
    }
  }

  // delete product images and videos from s3 bucket
  async deleteFiles(keys: string[]) {
    try {
      const deleteParams = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Delete: {
          Objects: keys.map((key) => ({ Key: key })),
        },
      };

      await this.s3.deleteObjects(deleteParams).promise();
    } catch (error) {
      throw new Error(`Failed to delete files from S3: ${error.message}`);
    }
  }

  async uploadFileToS3(
    fileName: string,
    fileStream: NodeJS.ReadableStream,
  ): Promise<string> {
    const uploadParams = {
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: fileName,
      Body: fileStream,
      // ACL: 'public-read',
    };

    return new Promise((resolve, reject) => {
      this.s3.upload(uploadParams, (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve(data.Location);
        }
      });
    });
  }
}
