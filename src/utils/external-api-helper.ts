import { Injectable, UnauthorizedException } from '@nestjs/common';
import env from 'src/config/env';
import { ExternalApiCaller } from './external-api-caller';
import * as AWSXRay from 'aws-xray-sdk';
import { LoggerService } from 'src/utils/logger-service';
import { EVENT_BUS_SOURCE } from 'src/config/constants';

@Injectable()
export class ExternalApiHelper {
  constructor(
    private readonly externalApiCaller: ExternalApiCaller,
    private readonly logger: LoggerService,
  ) {}

  async saveProductInViniculum(
    createdProduct,
    attributes_list,
    generateUrl_key,
  ) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'viniculumProductSave',
    // );
    try {
      const attributeObject = attributes_list.reduce(
        (result, { attribute_code, value }) => {
          result[attribute_code] = value;
          return result;
        },
        {},
      );

      const url = env.viniculum.create_product_url;
      const data = {
        RequestBody: JSON.stringify({
          skuList: [
            {
              skuCode: createdProduct.sku,
              skuName: attributeObject.name,
              classification: 'Normal',
              mrp: attributeObject.price,
              salePrice: attributeObject?.special_price
                ? attributeObject?.special_price
                : attributeObject.price,
              vendorCode: 'V1',
              baseCost: attributeObject?.special_price
                ? attributeObject?.special_price
                : attributeObject.price,
              taxCategory: '33069000',
              isActive: 'yes',
              lottableValidation: '0101',
              pdpURL: `https://www.dentalkart.com/${generateUrl_key}.html`,
              serialCount: '',
              isSaleable: 'yes',
              isBackOrder: 'no',
              backOrderQty: '0',
            },
          ],
        }),
        ApiKey: env.viniculum.api_key,
        ApiOwner: env.viniculum.owner,
      };

      const response = await this.externalApiCaller.post(
        url,
        {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data,
      );
      return response || null;
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  async updateProductAndSkuInViniculum(product: any) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'viniculumProductUpdate',
    // );
    try {
      let productDetails: any = product.items;

      const url = env.viniculum.update_product_url;
      const headers = {
        ApiKey: env.viniculum.api_key,
        ApiOwner: env.viniculum.owner,
        'Content-Type': 'application/json',
      };
      let slicedShortName = productDetails.attributes_list.name.slice(0, 10);
      const data = {
        request: [
          {
            skuCode: productDetails.sku,
            skuName: productDetails.attributes_list.name,
            skuShortName: slicedShortName,
            mrp: productDetails.attributes_list.price,
            salePrice: productDetails.attributes_list.special_price,
            taxCategory: productDetails.attributes_list.hsn_code,
          },
        ],
      };

      const response = await this.externalApiCaller.post(
        url,
        {
          ...headers,
        },
        data,
      );
      return response;
      // console.log(response.data, 'VINICULUM');
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  async sendProductsInElastisticSearchEventBus(data: any) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'productSyncEventBus',
    // );
    try {
      let url = env.catalog_eventbus.eventbusUrl;

      const headers = {
        'Content-Type': 'application/json',
        // Authorization: env.catalog_eventbus.eventbusAuthKey,
      };

      let eventbusData = {
        Detail: data,
        DetailType: 'dentalkart-catalog-service',
        Source: 'dev.dentalkart.catalog.service',
      };

      const response = await this.externalApiCaller.post(
        url,
        {
          ...headers,
        },
        eventbusData,
      );
      return response;
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  async sendProductsInMongoEventBus(data: any, eventIdArray: number[]) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'productSyncEventBus',
    // );
    try {
      let url = env.notifications_eventbus.eventbusUrl;

      console.log('++++Event_bus_url++++', url);

      const headers = {
        'Content-Type': 'application/json',
        Authorization: env.catalog_eventbus.eventbusAuthKey,
      };

      data['event_ids'] = eventIdArray?.join(',');

      let eventbusData = {
        Detail: data,
        DetailType: 'dentalkart-catalog-service',
        Source: EVENT_BUS_SOURCE.product.prod,
      };

      console.log('++++Event_bus_body++++', JSON.stringify(eventbusData));

      const response = await this.externalApiCaller.post(
        url,
        {
          ...headers,
        },
        eventbusData,
      );

      console.log('++++Event_bus_response++++', JSON.stringify(response));

      return response;
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    }
    // finally {
    // segment.close();
    // }
  }

  async sendCategoryInElastisticSearchEventBus(data: any) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'categorySyncEventBus',
    // );
    try {
      let url = env.catalog_eventbus.eventbusUrl;

      const headers = {
        'Content-Type': 'application/json',
        Authorization: env.catalog_eventbus.eventbusAuthKey,
      };

      let eventbusData = {
        Detail: data,
        DetailType: 'dentalkart-catalog-service',
        Source: EVENT_BUS_SOURCE.category.prod,
      };

      const response = await this.externalApiCaller.post(
        url,
        {
          ...headers,
        },
        eventbusData,
      );
      return response;
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  async sendCategoryInMongoDbEventBus(data: any, outBoxIds: Number[]) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'categorySyncEventBus',
    // );

    try {
      let url = env.notifications_eventbus.eventbusUrl;

      console.log('++++Event_bus_url++++', url);

      const headers = {
        'Content-Type': 'application/json',
        Authorization: env.catalog_eventbus.eventbusAuthKey,
      };
      data['event_ids'] = outBoxIds?.join(',');

      let eventbusData = {
        Detail: data,
        DetailType: 'dentalkart-catalog-service',
        Source: 'prod.dentalkart.catalog.category.service',
      };

      console.log('++++Event_bus_body++++', JSON.stringify(eventbusData));

      const response = await this.externalApiCaller.post(
        url,
        {
          ...headers,
        },

        eventbusData,
      );

      console.log('++++Event_bus_response++++', response);

      return response;
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  async sendUrlRewritesInElastisticSearchEventBus(data: any) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'urlRewriteSyncEventBus',
    // );
    try {
      let url = env.notifications_eventbus.eventbusUrl;

      data['url_rewrite_id'] = data.id;
      delete data.id;

      const headers = {
        'Content-Type': 'application/json',
        Authorization: env.catalog_eventbus.eventbusAuthKey,
      };

      let eventbusData = {
        Detail: data,
        DetailType: 'dentalkart-catalog-service',
        Source: 'dev.dentalkart.catalog.urlresolver.service',
      };

      const response = await this.externalApiCaller.post(
        url,
        {
          ...headers,
        },
        eventbusData,
      );

      return response;
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  async notifyUrlRewriteData(data: any, outboxIds: number[]) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'urlRewriteSyncEventBus',
    // );
    try {
      let url = env.notifications_eventbus.eventbusUrl;

      console.log('++++Event_bus_url++++', url);

      data['url_rewrite_id'] = data.id;
      data['event_ids'] = outboxIds?.join(',');
      delete data.id;

      const headers = {
        'Content-Type': 'application/json',
        Authorization: env.catalog_eventbus.eventbusAuthKey,
      };

      let eventbusData = {
        Detail: data,
        DetailType: 'dentalkart-catalog-service',
        Source: EVENT_BUS_SOURCE.url_rewrite.prod,
      };

      console.log('++++Event bus body++++', JSON.stringify(eventbusData));

      const response = await this.externalApiCaller.post(
        url,
        {
          ...headers,
        },
        eventbusData,
      );

      console.log('++++Event bus response++++', response);

      return response;
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  async fetchYoutubeVideoInfoHelper(videoId: string) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'fetchYoutubeVideoInfo',
    // );
    try {
      let url = env.googleInternalYoutubeDataApiUrl;
      // let headers = null;

      let Params = {
        part: 'snippet',
        id: videoId,
        key: env.googleApiKey,
      };

      const response = await this.externalApiCaller.get(url, null, Params);
      return response;
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  async fetchCustomerInfoInfoHelper(token: string) {
    // const segment = AWSXRay.getSegment().addNewSubsegment('fetchCustomerInfo');
    try {
      let url = env.getCustomerInfo;

      const headers = {
        Authorization: `Bearer ${token}`,
      };

      const response = await this.externalApiCaller.get(
        url,
        { ...headers },
        null,
      );
      return response;
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  async sendStockAlertEventsInNotificationsEventBus(data: any) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'stockAlertNotificationsEventBus',
    // );
    try {
      let url = env.notifications_eventbus.eventbusUrl;

      const headers = {
        'Content-Type': 'application/json',
        // Authorization: env.catalog_eventbus.eventbusAuthKey,
      };

      let eventbusData = {
        Detail: data,
        DetailType: 'notifications-event',
        Source: 'dentalkart.notifications.service',
      };

      const response = await this.externalApiCaller.post(
        url,
        {
          ...headers,
        },
        eventbusData,
      );
      return response;
    } catch (err) {
      // segment.addError(err);
      console.log(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  async getCustomerDetails(authToken: string) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   'GetCustomerTokenDetails',
    // );
    try {
      const url = env.dk.customer_base_url + `/customer/me`;
      const headers = { Authorization: `Bearer ${authToken}` };
      const response = await this.externalApiCaller.get(url, headers);
      if (!response) throw new UnauthorizedException('Invalid token');
      return response;
    } catch (err) {
      // segment.addError(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  syncProductAttributes(detail: object, detailType: string, source: string) {
    // const segment = AWSXRay.getSegment().addNewSubsegment(
    //   `syncProductAttributes-${source}-${detailType}`,
    // );
    try {
      const url = env.catalog_eventbus.eventbusUrl + `/v1/product/attributes`;
      const headers = {
        'Content-Type': 'application/json',
      };
      const body = {
        Detail: detail,
        DetailType: detailType,
        Source: source,
      };
      this.externalApiCaller
        .post(url, headers, body)
        .then((data) => this.logger.log(JSON.stringify(data)))
        .catch((e) =>
          this.logger.error(`syncProductAttributes-${source}-${detailType}`, e),
        );
    } catch (err) {
      // segment.addError(err);
      return null;
    } finally {
      // segment.close();
    }
  }

  getUserToken(authHeader: string) {
    const token = authHeader?.split(' ')[1];
    if (token === 'null') return undefined;
    return token;
  }
}
