import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as express from 'express';
import env from './config/env';
import { ValidationPipe } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';


async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  //Increase Payload size limit to 10 mb
  app.use(express.json({ limit: '10mb' }));
  
  // // Request logger middleware
  // app.use((req: Request, res: Response, next: NextFunction) => {
  //   // Clone the request body to avoid modification
  //   const requestBody = {...req.body};
    
  //   // Format everything for a single line log that's CloudWatch friendly
  //   const logData = {
  //     timestamp: new Date().toISOString(),
  //     endpoint: `${req.method} ${req.originalUrl}`,
  //     headers: JSON.stringify(req.headers),
  //     body: JSON.stringify(requestBody),
  //     query: JSON.stringify(req.query),
  //     params: JSON.stringify(req.params)
  //   };
    
  //   // Log as a single line string
  //   // console.log(`REQUEST: ${JSON.stringify(logData)}`);
    
  //   next();
  // });

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  );
  app.enableCors({ origin: '*', allowedHeaders: '*', methods: '*' });


  await app.listen(env.port);
}
bootstrap();
